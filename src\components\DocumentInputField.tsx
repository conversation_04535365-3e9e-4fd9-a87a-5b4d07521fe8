
import React from 'react';
import { Button } from '@/components/ui/button';

interface DocumentInputFieldProps {
  placeholder: string;
  onDocumentUpload: () => void;
  hasDocuments: boolean;
}

const DocumentInputField: React.FC<DocumentInputFieldProps> = ({ 
  placeholder, 
  onDocumentUpload,
  hasDocuments
}) => {
  return (
    <div className="p-4 border-t">
      {!hasDocuments ? (
        <div 
          className="flex items-center justify-center py-6 px-4 border-2 border-dashed rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={onDocumentUpload}
        >
          <p className="text-sm text-gray-500">
            Add documents to this chat first...
          </p>
        </div>
      ) : (
        <div className="relative">
          <input
            type="text"
            placeholder={placeholder}
            className="w-full p-3 pr-12 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#8B5CF6] focus:border-transparent"
            readOnly
            onClick={onDocumentUpload}
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex gap-2">
            <Button 
              size="sm"
              variant="ghost" 
              className="h-8 w-8 p-0 rounded-full"
              onClick={onDocumentUpload}
            >
              <span className="text-xs text-muted-foreground">AI Agent</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentInputField;
