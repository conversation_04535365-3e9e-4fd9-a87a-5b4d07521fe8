import React, { useState, useEffect } from 'react';
import { useWebSocketContext } from '../contexts/WebSocketContext';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { ScrollArea } from './ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { 
  Activity, 
  MessageSquare, 
  FileText, 
  Settings, 
  Trash2,
  Send,
  RefreshCw,
  X
} from 'lucide-react';

interface WebSocketDebugPanelProps {
  className?: string;
}

const WebSocketDebugPanel: React.FC<WebSocketDebugPanelProps> = ({ className = '' }) => {
  const webSocket = useWebSocketContext();
  const [testFileId, setTestFileId] = useState('test-file-123');
  const [testSessionId, setTestSessionId] = useState('test-session-456');
  const [testMessage, setTestMessage] = useState('Hello, this is a test message');
  const [eventLog, setEventLog] = useState<Array<{
    timestamp: string;
    type: string;
    data: any;
  }>>([]);

  // Log WebSocket events for debugging
  useEffect(() => {
    const logEvent = (type: string, data: any) => {
      setEventLog(prev => [...prev.slice(-49), {
        timestamp: new Date().toLocaleTimeString(),
        type,
        data
      }]);
    };

    // Monitor connection changes
    if (webSocket.isConnected) {
      logEvent('CONNECTION', { status: 'connected' });
    } else {
      logEvent('CONNECTION', { status: 'disconnected', error: webSocket.connectionError });
    }
  }, [webSocket.isConnected, webSocket.connectionError]);

  // Monitor file updates
  useEffect(() => {
    const fileUpdates = Object.keys(webSocket.fileUpdates);
    if (fileUpdates.length > 0) {
      fileUpdates.forEach(fileId => {
        const update = webSocket.fileUpdates[fileId];
        setEventLog(prev => [...prev.slice(-49), {
          timestamp: new Date().toLocaleTimeString(),
          type: 'FILE_UPDATE',
          data: { fileId, ...update }
        }]);
      });
    }
  }, [webSocket.fileUpdates]);

  // Monitor chat streams
  useEffect(() => {
    const chatSessions = Object.keys(webSocket.chatStreams);
    if (chatSessions.length > 0) {
      chatSessions.forEach(sessionId => {
        const stream = webSocket.getChatStream(sessionId);
        if (stream) {
          setEventLog(prev => [...prev.slice(-49), {
            timestamp: new Date().toLocaleTimeString(),
            type: 'CHAT_STREAM',
            data: { sessionId, ...stream }
          }]);
        }
      });
    }
  }, [webSocket.chatStreams, webSocket.getChatStream]);

  const handleJoinFileRoom = () => {
    if (testFileId.trim()) {
      webSocket.joinFileRoom(testFileId.trim());
      setEventLog(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        type: 'JOIN_FILE_ROOM',
        data: { fileId: testFileId.trim() }
      }]);
    }
  };

  const handleJoinChatRoom = () => {
    if (testSessionId.trim()) {
      webSocket.joinChatRoom(testSessionId.trim());
      setEventLog(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        type: 'JOIN_CHAT_ROOM',
        data: { sessionId: testSessionId.trim() }
      }]);
    }
  };

  const handleLeaveFileRoom = () => {
    if (testFileId.trim()) {
      webSocket.leaveFileRoom(testFileId.trim());
      setEventLog(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        type: 'LEAVE_FILE_ROOM',
        data: { fileId: testFileId.trim() }
      }]);
    }
  };

  const handleLeaveChatRoom = () => {
    if (testSessionId.trim()) {
      webSocket.leaveChatRoom(testSessionId.trim());
      setEventLog(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        type: 'LEAVE_CHAT_ROOM',
        data: { sessionId: testSessionId.trim() }
      }]);
    }
  };

  const clearEventLog = () => {
    setEventLog([]);
  };

  const getEventBadgeColor = (type: string) => {
    switch (type) {
      case 'CONNECTION': return 'bg-blue-500';
      case 'FILE_UPDATE': return 'bg-green-500';
      case 'CHAT_STREAM': return 'bg-purple-500';
      case 'JOIN_FILE_ROOM':
      case 'JOIN_CHAT_ROOM': return 'bg-orange-500';
      case 'LEAVE_FILE_ROOM':
      case 'LEAVE_CHAT_ROOM': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  if (process.env.NODE_ENV === 'production') {
    return null; // Don't show debug panel in production
  }

  return (
    <Card className={`w-full max-w-4xl ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          WebSocket Debug Panel
          <Badge variant={webSocket.isConnected ? 'default' : 'destructive'}>
            {webSocket.isConnected ? 'Connected' : 'Disconnected'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="controls" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="controls">Controls</TabsTrigger>
            <TabsTrigger value="status">Status</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
            <TabsTrigger value="data">Data</TabsTrigger>
          </TabsList>

          <TabsContent value="controls" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Connection Controls */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium">Connection</h3>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={webSocket.reconnect}
                    disabled={webSocket.isConnected}
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Reconnect
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={webSocket.disconnect}
                    disabled={!webSocket.isConnected}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Disconnect
                  </Button>
                </div>
              </div>

              {/* File Room Controls */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium">File Rooms</h3>
                <div className="flex gap-2">
                  <Input
                    placeholder="File ID"
                    value={testFileId}
                    onChange={(e) => setTestFileId(e.target.value)}
                    className="text-xs"
                  />
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleJoinFileRoom}>
                    <FileText className="h-4 w-4 mr-1" />
                    Join
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleLeaveFileRoom}>
                    <X className="h-4 w-4 mr-1" />
                    Leave
                  </Button>
                </div>
              </div>

              {/* Chat Room Controls */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium">Chat Rooms</h3>
                <div className="flex gap-2">
                  <Input
                    placeholder="Session ID"
                    value={testSessionId}
                    onChange={(e) => setTestSessionId(e.target.value)}
                    className="text-xs"
                  />
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleJoinChatRoom}>
                    <MessageSquare className="h-4 w-4 mr-1" />
                    Join
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleLeaveChatRoom}>
                    <X className="h-4 w-4 mr-1" />
                    Leave
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="status" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Connection Status</h3>
                <div className="text-xs space-y-1">
                  <div>Connected: {webSocket.isConnected ? 'Yes' : 'No'}</div>
                  <div>Error: {webSocket.connectionError || 'None'}</div>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-2">File Updates</h3>
                <div className="text-xs">
                  Active Files: {Object.keys(webSocket.fileUpdates).length}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-2">Chat Streams</h3>
                <div className="text-xs">
                  Active Sessions: {Object.keys(webSocket.chatStreams).length}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="events" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium">Event Log</h3>
              <Button variant="outline" size="sm" onClick={clearEventLog}>
                <Trash2 className="h-4 w-4 mr-1" />
                Clear
              </Button>
            </div>
            <ScrollArea className="h-64 w-full border rounded p-2">
              <div className="space-y-2">
                {eventLog.map((event, index) => (
                  <div key={index} className="text-xs border-b pb-2">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge className={`${getEventBadgeColor(event.type)} text-white text-xs`}>
                        {event.type}
                      </Badge>
                      <span className="text-gray-500">{event.timestamp}</span>
                    </div>
                    <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                      {JSON.stringify(event.data, null, 2)}
                    </pre>
                  </div>
                ))}
                {eventLog.length === 0 && (
                  <div className="text-center text-gray-500 py-8">
                    No events logged yet
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="data" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium mb-2">File Updates</h3>
                <ScrollArea className="h-32 w-full border rounded p-2">
                  <pre className="text-xs">
                    {JSON.stringify(webSocket.fileUpdates, null, 2)}
                  </pre>
                </ScrollArea>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-2">Chat Streams</h3>
                <ScrollArea className="h-32 w-full border rounded p-2">
                  <pre className="text-xs">
                    {JSON.stringify(webSocket.chatStreams, null, 2)}
                  </pre>
                </ScrollArea>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default WebSocketDebugPanel;
