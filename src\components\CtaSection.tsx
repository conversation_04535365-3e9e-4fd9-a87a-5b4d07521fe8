
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';

const CtaSection: React.FC = () => {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-docai-purple to-docai-blue p-8 md:p-12">
          <div className="relative z-10">
            <div className="max-w-3xl mx-auto text-center text-white space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold">
                Ready to transform how you work with documents?
              </h2>
              <p className="text-lg opacity-90">
                Join thousands of professionals who have already simplified their document workflows with AnyDocAI.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4 pt-4">
                <Button size="lg" variant="secondary" className="gap-2 group">
                  Get started free
                  <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button variant="outline" size="lg" className="bg-white/10 text-white border-white/20 hover:bg-white/20">
                  Request a demo
                </Button>
              </div>
              <p className="text-sm opacity-80 pt-4">
                No credit card required. Free plan includes 10 uploads/day.
              </p>
            </div>
          </div>
          <div className="absolute inset-0 bg-gradient-to-r from-docai-purple/20 to-docai-blue/20 backdrop-blur-sm"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,white_0,transparent_65%)] opacity-10"></div>
        </div>
      </div>
    </section>
  );
};

export default CtaSection;
