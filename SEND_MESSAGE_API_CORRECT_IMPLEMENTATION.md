# Send Message API - Correct Implementation ✅

## API Specification

**Endpoint**: `POST /api/chat/sessions/{session_id}/messages`

**Description**: Send a message in a chat session

**Parameters**:
- `session_id` (path parameter): ID of the session

**Request Body** (`ChatRequest`):
```json
{
  "message": "string",
  "chat_history": [
    {
      "role": "string",
      "content": "string"
    }
  ],
  "use_agent": false
}
```

**Response** (`ChatResponse`):
```json
{
  "message": "string",
  "sources": [],
  "metadata": {}
}
```

## ✅ Correct Implementation

### 1. **API Types (chatApi.ts)**

```typescript
// ✅ Proper type definitions
export interface ChatHistoryMessage {
  role: string;
  content: string;
}

export interface ChatRequest {
  message: string;
  chat_history?: ChatHistoryMessage[];
  use_agent?: boolean;
}

export interface ChatResponse {
  message: string;
  sources?: unknown[];
  metadata?: Record<string, unknown>;
}
```

### 2. **API Endpoint (chatApi.ts)**

```typescript
// ✅ Correct endpoint configuration
export const CHAT_ENDPOINTS = {
  SESSION_MESSAGES: (sessionId: string) => `${API_URL}/chat/sessions/${sessionId}/messages`,
  // ... other endpoints
};

// ✅ Correct API function
export const chatApi = {
  sendMessage: async (sessionId: string, request: ChatRequest): Promise<{ data: ChatResponse }> => {
    return apiRequest(CHAT_ENDPOINTS.SESSION_MESSAGES(sessionId), {
      method: 'POST',
      body: JSON.stringify(request),
    });
  },
  // ... other functions
};
```

### 3. **Redux Integration (chatSlice.ts)**

```typescript
// ✅ Correct imports
import { chatApi } from '@/services/chatApi';

// ✅ Correct Redux thunk
export const sendMessage = createAsyncThunk(
  'chat/sendMessage',
  async ({
    sessionId,
    message,
    chatHistory = [],
    useAgent = false
  }: {
    sessionId: string;
    message: string;
    chatHistory?: ChatMessage[];
    useAgent?: boolean
  }, { rejectWithValue }) => {
    try {
      // Convert ChatMessage[] to ChatHistoryMessage[] format expected by API
      const formattedChatHistory = chatHistory.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // Use the correct chatApi from chatApi.ts which has the proper endpoint
      const response = await chatApi.sendMessage(sessionId, {
        message,
        chat_history: formattedChatHistory,
        use_agent: useAgent
      });

      return response.data; // Return the ChatResponse data
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to send message');
    }
  }
);
```

### 4. **Redux Reducer (chatSlice.ts)**

```typescript
// ✅ Correct response handling
.addCase(sendMessage.fulfilled, (state, action) => {
  state.isLoading = false;
  // action.payload is now ChatResponse directly
  if (action.payload && action.payload.message) {
    const assistantMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'assistant',
      content: action.payload.message,
      timestamp: new Date().toISOString()
    };
    state.messages = [...state.messages, assistantMessage];
  }
})
```

## 🔄 How It Works

### Request Flow:
1. **Component**: User sends message
2. **Redux**: `dispatch(sendMessage({ sessionId, message, chatHistory, useAgent }))`
3. **API Call**: `POST /api/chat/sessions/{sessionId}/messages`
4. **Request Body**:
   ```json
   {
     "message": "What is this document about?",
     "chat_history": [
       {
         "role": "user",
         "content": "Hello"
       },
       {
         "role": "assistant",
         "content": "Hi! How can I help you?"
       }
     ],
     "use_agent": false
   }
   ```
5. **Response**: `ChatResponse` with assistant's message
6. **Redux**: Add assistant message to state

### Component Usage:
```typescript
// ✅ Correct usage in component
const sendMessageHandler = useCallback(async () => {
  if (!inputMessage.trim() || isLoading || !sessionId) return;

  const userMessage: ChatMessage = {
    id: `msg-${Date.now()}`,
    role: 'user',
    content: inputMessage.trim(),
    timestamp: new Date().toISOString()
  };

  // Add user message immediately
  dispatch(addMessage(userMessage));
  setInputMessage('');

  try {
    // Send message with chat history
    await dispatch(sendMessage({
      sessionId,
      message: userMessage.content,
      chatHistory: messages, // Current conversation history
      useAgent: false
    })).unwrap();

    // Assistant response will be added by Redux reducer
  } catch (error) {
    console.error('Error sending message:', error);
    // Handle error...
  }
}, [inputMessage, sessionId, messages, dispatch]);
```

## ✅ Key Differences from Previous Implementation

### ❌ **Previous (Wrong)**:
- Mixed up different API files (`api.ts` vs `chatApi.ts`)
- Wrong endpoint paths
- Incorrect type definitions
- Conflicting imports

### ✅ **Current (Correct)**:
- Uses `chatApi.ts` which has the correct `/api/chat/sessions/{sessionId}/messages` endpoint
- Proper `ChatRequest` and `ChatResponse` types
- Clean imports and no conflicts
- Correct response handling in Redux

## 🚀 Benefits

1. **Correct API Endpoint**: Uses the proper `/api/chat/sessions/{session_id}/messages` endpoint
2. **Type Safety**: Proper TypeScript types for request and response
3. **Chat History**: Sends complete conversation context
4. **Agent Support**: `use_agent` parameter for AI agent control
5. **WebSocket Ready**: Compatible with streaming responses
6. **Error Handling**: Comprehensive error handling
7. **Redux Integration**: Full state management

## 📋 Files Updated

- ✅ `src/services/chatApi.ts` - Correct API implementation
- ✅ `src/features/chat/chatSlice.ts` - Correct Redux integration
- ✅ `src/services/api.ts` - Cleaned up duplicate types

## 🎯 Testing

### Test the API call:
```bash
curl -X POST "http://localhost:8000/api/chat/sessions/{session_id}/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "message": "Hello, what can you tell me about this document?",
    "chat_history": [],
    "use_agent": false
  }'
```

### Expected Response:
```json
{
  "message": "Hello! I can help you analyze and discuss the document. What specific aspects would you like to know about?",
  "sources": [],
  "metadata": {}
}
```

The send message API is now correctly implemented! 🚀
