import { createContext, useContext, ReactNode, useMemo } from 'react';
import { useWebSocket } from '../hooks/useWebSocket';
import { useSelector } from 'react-redux';
import { RootState } from '../app/store';

interface WebSocketContextType {
  isConnected: boolean;
  connectionError: string | null;
  joinFileRoom: (fileId: string) => void;
  joinChatRoom: (chatSessionId: string) => void;
  leaveFileRoom: (fileId: string) => void;
  leaveChatRoom: (chatSessionId: string) => void;
  getFileStatus: (fileId: string) => any;
  getProcessingProgress: (fileId: string) => any;
  getChatStream: (sessionId: string) => any;
  clearChatStream: (sessionId: string) => void;
  clearAllChatStreams: () => void;
  fileUpdates: Record<string, any>;
  processingProgress: Record<string, any>;
  chatStreams: Record<string, any>;
  // Additional utility methods
  reconnect: () => void;
  disconnect: () => void;
  // System message functions
  systemMessages: Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
    metadata?: any;
  }>;
  getSystemMessages: () => Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
    metadata?: any;
  }>;
  clearSystemMessages: () => void;
  getUnreadSystemMessages: () => Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
    metadata?: any;
  }>;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

interface WebSocketProviderProps {
  children: ReactNode;
}

export const WebSocketProvider = ({ children }: WebSocketProviderProps) => {
  // Get user from Redux store
  const user = useSelector((state: RootState) => state.auth.user);

  // Memoize userId to prevent unnecessary re-renders
  const userId = useMemo(() => {
    try {
      // Try Redux store first
      if (user?.id) {
        return user.id;
      }

      // Fallback to localStorage/sessionStorage
      const storedUser = JSON.parse(
        localStorage.getItem('user') ||
        sessionStorage.getItem('user') ||
        '{}'
      );
      return storedUser.id || null;
    } catch (error) {
      console.error('Error getting user ID:', error);
      return null;
    }
  }, [user?.id]);

  const webSocket = useWebSocket(userId);

  return (
    <WebSocketContext.Provider value={webSocket}>
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocketContext = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocketContext must be used within a WebSocketProvider');
  }
  return context;
};
