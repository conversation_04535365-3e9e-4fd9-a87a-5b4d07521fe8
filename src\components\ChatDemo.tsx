
import React from 'react';
import { Button } from '@/components/ui/button';
import { Search, FileText, Send } from 'lucide-react';

const ChatDemo: React.FC = () => {
  return (
    <div className="border rounded-xl shadow-lg bg-card overflow-hidden">
      <div className="border-b p-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="h-3 w-3 rounded-full bg-docai-purple"></div>
          <span className="font-medium text-sm">AnyDocAI Chat</span>
        </div>
        <div className="flex items-center gap-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <div className="h-4 w-[1px] bg-border"></div>
          <FileText className="h-4 w-4 text-muted-foreground" />
        </div>
      </div>
      <div className="p-4 space-y-4 min-h-[300px] max-h-[450px] overflow-y-auto">
        <div className="flex items-start gap-3">
          <div className="h-8 w-8 rounded-full bg-gradient-to-r from-docai-purple to-docai-blue flex items-center justify-center text-white font-medium text-sm">
            AI
          </div>
          <div className="bg-muted rounded-lg p-3 text-sm max-w-[80%]">
            <p>Hi there! I'm your AI document assistant. Upload any file and ask me questions about it.</p>
          </div>
        </div>
        <div className="flex items-start gap-3 justify-end">
          <div className="bg-primary/10 rounded-lg p-3 text-sm max-w-[80%]">
            <p>Can you summarize this report for me in 3 bullet points?</p>
          </div>
          <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-muted-foreground font-medium text-sm">
            U
          </div>
        </div>
        <div className="flex items-start gap-3">
          <div className="h-8 w-8 rounded-full bg-gradient-to-r from-docai-purple to-docai-blue flex items-center justify-center text-white font-medium text-sm">
            AI
          </div>
          <div className="bg-muted rounded-lg p-3 text-sm max-w-[80%]">
            <p>Here's a summary of the Q1 Financial Report:</p>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Revenue increased by 27% YoY, reaching $4.2M due to expansion into European markets</li>
              <li>Operating costs rose 12%, mainly from hiring 15 new engineers and opening a new office</li>
              <li>Customer acquisition cost decreased by 18%, with retention rates improving from 72% to 85%</li>
            </ul>
            <p className="mt-2 text-xs text-muted-foreground">Source: Q1_Financial_Report.pdf, pages 3-7</p>
          </div>
        </div>
      </div>
      <div className="p-3 border-t">
        <div className="relative">
          <input 
            type="text" 
            placeholder="Ask any question about your documents..." 
            className="w-full rounded-full border bg-background px-4 py-2 pr-12 text-sm focus:outline-none focus:ring-2 focus:ring-primary"
          />
          <Button size="sm" className="absolute right-1 top-1/2 -translate-y-1/2 rounded-full w-8 h-8 p-0">
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatDemo;
