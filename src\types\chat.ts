
export interface Document {
  id: string;
  name: string;
  type: string;
  size: string;
  dateUploaded: string;
  content?: string;
  url?: string;
  file_id?: string;
  status?: string;
  s3_key?: string;
  created_at?: string;
}

export interface Source {
  documentId: string;
  pages?: number[];
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  sources?: Source[];
  metadata?: Record<string, unknown>;
}

export interface ChatSession {
  id: string;
  name: string;
  lastMessageTime: string;
  documentIds: string[];
}
