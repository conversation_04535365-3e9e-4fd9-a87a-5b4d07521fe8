import React, { useEffect, useState } from 'react';
import { useWebSocketContext } from '../contexts/WebSocketContext';
import { Alert, AlertDescription, AlertTitle } from './ui/alert';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { ScrollArea } from './ui/scroll-area';
import { 
  Bell, 
  X, 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  XCircle,
  Clock,
  Trash2
} from 'lucide-react';
import { toast } from 'sonner';

interface SystemMessageNotificationsProps {
  className?: string;
  showToasts?: boolean;
  maxVisible?: number;
}

const SystemMessageNotifications: React.FC<SystemMessageNotificationsProps> = ({
  className = '',
  showToasts = true,
  maxVisible = 5
}) => {
  const webSocket = useWebSocketContext();
  const [dismissedMessages, setDismissedMessages] = useState<Set<string>>(new Set());
  const [isExpanded, setIsExpanded] = useState(false);

  // Show toast notifications for new system messages
  useEffect(() => {
    if (!showToasts) return;

    const newMessages = webSocket.systemMessages.filter(
      msg => !dismissedMessages.has(msg.id) && 
             (msg.type === 'error' || msg.type === 'warning')
    );

    newMessages.forEach(msg => {
      if (msg.type === 'error') {
        toast.error(msg.message, {
          description: `System notification at ${new Date(msg.timestamp).toLocaleTimeString()}`,
          action: {
            label: 'Dismiss',
            onClick: () => dismissMessage(msg.id)
          }
        });
      } else if (msg.type === 'warning') {
        toast.warning(msg.message, {
          description: `System notification at ${new Date(msg.timestamp).toLocaleTimeString()}`,
          action: {
            label: 'Dismiss',
            onClick: () => dismissMessage(msg.id)
          }
        });
      }
    });
  }, [webSocket.systemMessages, dismissedMessages, showToasts]);

  const dismissMessage = (messageId: string) => {
    setDismissedMessages(prev => new Set([...prev, messageId]));
  };

  const clearAllMessages = () => {
    webSocket.clearSystemMessages();
    setDismissedMessages(new Set());
  };

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'info': return <Info className="h-4 w-4 text-blue-500" />;
      default: return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  const getAlertVariant = (type: string): "default" | "destructive" => {
    return type === 'error' ? 'destructive' : 'default';
  };

  const visibleMessages = webSocket.systemMessages
    .filter(msg => !dismissedMessages.has(msg.id))
    .slice(-maxVisible);

  const unreadCount = webSocket.getUnreadSystemMessages().filter(
    msg => !dismissedMessages.has(msg.id)
  ).length;

  if (visibleMessages.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Header with notification count */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Bell className="h-4 w-4" />
          <span className="text-sm font-medium">System Notifications</span>
          {unreadCount > 0 && (
            <Badge variant="destructive" className="text-xs">
              {unreadCount}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-xs"
          >
            {isExpanded ? 'Collapse' : 'Expand'}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllMessages}
            className="text-xs"
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Clear All
          </Button>
        </div>
      </div>

      {/* Messages */}
      {isExpanded ? (
        <ScrollArea className="max-h-64 w-full">
          <div className="space-y-2">
            {webSocket.systemMessages.map(message => (
              <Alert 
                key={message.id} 
                variant={getAlertVariant(message.type)}
                className={`relative ${dismissedMessages.has(message.id) ? 'opacity-50' : ''}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-2 flex-1">
                    {getMessageIcon(message.type)}
                    <div className="flex-1">
                      <AlertTitle className="text-sm">
                        {message.type.charAt(0).toUpperCase() + message.type.slice(1)} Notification
                      </AlertTitle>
                      <AlertDescription className="text-xs">
                        {message.message}
                      </AlertDescription>
                      <div className="flex items-center gap-2 mt-1">
                        <Clock className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </span>
                        {message.metadata && (
                          <Badge variant="outline" className="text-xs">
                            {Object.keys(message.metadata).length} details
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => dismissMessage(message.id)}
                    className="h-6 w-6 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </Alert>
            ))}
          </div>
        </ScrollArea>
      ) : (
        <div className="space-y-2">
          {visibleMessages.map(message => (
            <Alert 
              key={message.id} 
              variant={getAlertVariant(message.type)}
              className="relative"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 flex-1">
                  {getMessageIcon(message.type)}
                  <div className="flex-1">
                    <AlertDescription className="text-sm">
                      {message.message}
                    </AlertDescription>
                    <div className="flex items-center gap-2 mt-1">
                      <Clock className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-500">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => dismissMessage(message.id)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </Alert>
          ))}
        </div>
      )}

      {/* Show more indicator */}
      {webSocket.systemMessages.length > maxVisible && !isExpanded && (
        <div className="text-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(true)}
            className="text-xs text-gray-500"
          >
            +{webSocket.systemMessages.length - maxVisible} more notifications
          </Button>
        </div>
      )}
    </div>
  );
};

export default SystemMessageNotifications;
