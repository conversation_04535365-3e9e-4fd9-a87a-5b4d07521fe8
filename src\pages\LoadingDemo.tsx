import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  PulseDotsLoader,
  SpinningRingLoader,
  BouncingBallsLoader,
  MorphingSquaresLoader,
  WaveLoader,
  RotatingHexagonLoader,
  TypingDotsLoader,
  ProgressRingLoader,
  FloatingParticlesLoader,
  DNAHelixLoader
} from '@/components/ui/loading-animations';
import { LoadingOverlay, InlineLoader, LoadingButton } from '@/components/ui/loading-overlay';
import { useApiLoading } from '@/hooks/useLoadingAnimation';

const LoadingDemo: React.FC = () => {
  const [overlayDemo, setOverlayDemo] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const { startLoading, stopLoading, isLoading } = useApiLoading();

  const showOverlay = (type: string) => {
    setOverlayDemo(type);
    setTimeout(() => setOverlayDemo(null), 3000);
  };

  const simulateProgress = () => {
    setProgress(0);
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const testApiLoading = (key: any) => {
    startLoading(key);
    setTimeout(() => stopLoading(key), 3000);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🎨 Loading Animations Showcase
          </h1>
          <p className="text-lg text-gray-600">
            Stylish and engaging loading animations to keep users entertained!
          </p>
        </div>

        {/* Basic Loaders Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Pulse Dots</CardTitle>
              <CardDescription>Perfect for general loading states</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <PulseDotsLoader size="lg" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Spinning Ring</CardTitle>
              <CardDescription>Great for API calls and processing</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <SpinningRingLoader size={50} />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Bouncing Balls</CardTitle>
              <CardDescription>Fun animation for chat messages</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <BouncingBallsLoader />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Morphing Squares</CardTitle>
              <CardDescription>Ideal for document processing</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <MorphingSquaresLoader />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Wave Animation</CardTitle>
              <CardDescription>Perfect for file uploads</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <WaveLoader />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Rotating Hexagon</CardTitle>
              <CardDescription>Great for session creation</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <RotatingHexagonLoader />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Typing Dots</CardTitle>
              <CardDescription>AI response indicator</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <TypingDotsLoader />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Progress Ring</CardTitle>
              <CardDescription>Shows completion percentage</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <ProgressRingLoader progress={progress} size={70} />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Floating Particles</CardTitle>
              <CardDescription>Elegant background loading</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <FloatingParticlesLoader />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>DNA Helix</CardTitle>
              <CardDescription>Complex processing animation</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <DNAHelixLoader />
            </CardContent>
          </Card>
        </div>

        {/* Interactive Demos */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Loading Overlays</CardTitle>
              <CardDescription>Full-screen loading experiences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={() => showOverlay('blur')}
                className="w-full"
              >
                Show Blur Overlay
              </Button>
              <Button 
                onClick={() => showOverlay('dark')}
                variant="outline"
                className="w-full"
              >
                Show Dark Overlay
              </Button>
              <Button 
                onClick={simulateProgress}
                variant="secondary"
                className="w-full"
              >
                Simulate Progress
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Loading Buttons</CardTitle>
              <CardDescription>Buttons with integrated loading states</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <LoadingButton
                isLoading={isLoading('CREATE_SESSION')}
                loadingType="rotating-hexagon"
                loadingText="Creating..."
                onClick={() => testApiLoading('CREATE_SESSION')}
                className="w-full"
              >
                Create Session
              </LoadingButton>
              
              <LoadingButton
                isLoading={isLoading('SEND_MESSAGE')}
                loadingType="bouncing-balls"
                loadingText="Sending..."
                onClick={() => testApiLoading('SEND_MESSAGE')}
                variant="secondary"
                className="w-full"
              >
                Send Message
              </LoadingButton>

              <LoadingButton
                isLoading={isLoading('UPLOAD_DOCUMENT')}
                loadingType="wave"
                loadingText="Uploading..."
                onClick={() => testApiLoading('UPLOAD_DOCUMENT')}
                variant="outline"
                className="w-full"
              >
                Upload Document
              </LoadingButton>
            </CardContent>
          </Card>
        </div>

        {/* Inline Loaders */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Inline Loaders</CardTitle>
            <CardDescription>Small loaders for inline use</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-4">
                <InlineLoader type="pulse-dots" message="Loading..." size="sm" />
              </div>
              <div className="flex items-center space-x-4">
                <InlineLoader type="spinning-ring" message="Processing..." size="md" />
              </div>
              <div className="flex items-center space-x-4">
                <InlineLoader type="wave" message="Uploading..." size="sm" />
              </div>
              <div className="flex items-center space-x-4">
                <InlineLoader type="morphing-squares" message="Analyzing..." size="md" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Usage Examples */}
        <Card>
          <CardHeader>
            <CardTitle>🚀 Usage in Your App</CardTitle>
            <CardDescription>These animations are automatically used throughout the chat application</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-semibold mb-2">Chat Operations:</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• Session creation: Rotating Hexagon</li>
                  <li>• Message sending: Bouncing Balls</li>
                  <li>• AI responses: Typing Dots</li>
                  <li>• Session loading: Floating Particles</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Document Operations:</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• File uploads: Wave Animation</li>
                  <li>• Document processing: DNA Helix</li>
                  <li>• Progress tracking: Progress Ring</li>
                  <li>• General loading: Pulse Dots</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Demo Overlays */}
      <LoadingOverlay
        isVisible={overlayDemo === 'blur'}
        type="dna-helix"
        message="This is a blur overlay demo!"
        backdrop="blur"
        size="lg"
      />
      
      <LoadingOverlay
        isVisible={overlayDemo === 'dark'}
        type="floating-particles"
        message="This is a dark overlay demo!"
        backdrop="dark"
        size="md"
      />
    </div>
  );
};

export default LoadingDemo;
