// Security headers middleware for React application
// This middleware adds security headers to the application's meta tags

import React from 'react';

import { Helmet } from 'react-helmet';

// Security headers configuration
const securityHeaders = {
  // Content Security Policy - restricts sources of content
  'Content-Security-Policy':
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
    "img-src 'self' data: blob:; " +
    "font-src 'self' https://fonts.gstatic.com; " +
    "connect-src 'self' http://localhost:8000 https://api.anydocai.com; " +
    "frame-src 'self'; " +
    "object-src 'none';",

  // Prevents browsers from MIME-sniffing a response away from the declared content-type
  'X-Content-Type-Options': 'nosniff',

  // Controls how much referrer information should be included with requests
  'Referrer-Policy': 'strict-origin-when-cross-origin',

  // Prevents rendering if XSS attack is detected
  'X-XSS-Protection': '1; mode=block',

  // HTTP Strict Transport Security - forces HTTPS
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',

  // Permissions Policy - controls browser features
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), interest-cohort=()'
};

// SecurityHeaders component to add security headers to the application
const SecurityHeaders: React.FC = () => {
  return (
    <Helmet>
      {Object.entries(securityHeaders).map(([key, value]) => {
        return <meta key={key} httpEquiv={key} content={value} />;
      })}
    </Helmet>
  );
};

export default SecurityHeaders;
