
import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

const faqs = [
  {
    question: "What kinds of documents can I use with AnyDocAI?",
    answer: "AnyDocAI supports a wide range of document formats including PDFs, Word documents (.docx), PowerPoint presentations (.pptx), Excel spreadsheets (.xlsx), text files (.txt), and more. You can upload multiple documents in different formats to the same chat session."
  },
  {
    question: "How accurate is AnyDocAI at understanding my documents?",
    answer: "AnyDocAI is powered by the latest large language models (GPT-4 Turbo and Claude 3) for excellent comprehension of text, tables, and images within documents. Our specialized document processing pipeline ensures high fidelity understanding of layouts, tables, and complex formatting."
  },
  {
    question: "Is my data secure when I upload documents?",
    answer: "Yes! Security is our top priority. All documents are encrypted in transit and at rest. We use enterprise-grade Wasabi S3 storage with strict access controls. Your documents are only accessible to you and team members you explicitly share with. We never use your documents to train our models."
  },
  {
    question: "What's the difference between the free and paid plans?",
    answer: "Our free plan allows 10 document uploads per day with a maximum size of 5MB each and access to our standard AI model. Paid plans offer higher upload limits (50-unlimited uploads/day), larger file sizes (up to 50MB), priority processing, and access to our most advanced AI models for deeper document understanding."
  },
  {
    question: "Can I chat with multiple documents at once?",
    answer: "Absolutely! This is one of AnyDocAI's most powerful features. You can upload multiple related documents to a single chat session and ask questions across all of them. For example, compare information between quarterly reports or find connections between academic papers."
  },
  {
    question: "How do I share documents or chat sessions with my team?",
    answer: "On Business and Enterprise plans, you can create team workspaces and invite members. Within a workspace, you can share specific chat sessions and their associated documents. Team members can view documents, see previous chat messages, and continue conversations with the documents."
  }
];

const FaqSection: React.FC = () => {
  return (
    <section id="faq" className="py-16 bg-muted/30">
      <div className="container px-4 md:px-6">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Frequently Asked <span className="gradient-text">Questions</span>
          </h2>
          <p className="text-muted-foreground">
            Everything you need to know about AnyDocAI
          </p>
        </div>
        <div className="max-w-3xl mx-auto">
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-left font-semibold">{faq.question}</AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
};

export default FaqSection;
