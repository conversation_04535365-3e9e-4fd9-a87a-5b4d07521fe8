
import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../app/hooks';
import { selectIsAuthenticated, getCurrentUser, selectUser, logout, checkTokenExpiration } from '../features/auth/authSlice';
import { toast } from 'sonner';

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const user = useAppSelector(selectUser);
  const [isLoading, setIsLoading] = useState(true);

  // Handle authentication errors
  const handleAuthError = useCallback((event: CustomEvent) => {
    console.warn('Auth error event received:', event.detail);

    // Show toast notification
    toast.error(event.detail.message || 'Your session has expired. Please log in again.');

    // Logout the user
    dispatch(logout());

    // Redirect to login page
    navigate('/auth');
  }, [dispatch, navigate]);

  useEffect(() => {
    // Add event listener for auth errors
    window.addEventListener('auth-error', handleAuthError as EventListener);

    // Clean up event listener
    return () => {
      window.removeEventListener('auth-error', handleAuthError as EventListener);
    };
  }, [handleAuthError]);

  useEffect(() => {
    // Try to get current user when component mounts
    const checkAuth = async () => {
      try {
        setIsLoading(true);
        await dispatch(getCurrentUser()).unwrap();
      } catch (error) {
        console.error('Authentication check failed:', error);
        navigate('/auth');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [dispatch, navigate]);

  useEffect(() => {
    // If authentication state changes to not authenticated, redirect to login
    if (!isLoading && !isAuthenticated) {
      navigate('/auth');
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Periodically check for token expiration
  useEffect(() => {
    if (isAuthenticated) {
      // Check token expiration every minute
      const tokenCheckInterval = setInterval(() => {
        dispatch(checkTokenExpiration());
      }, 60000); // 1 minute

      return () => {
        clearInterval(tokenCheckInterval);
      };
    }
  }, [isAuthenticated, dispatch]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="h-8 w-8 border-4 border-t-transparent border-primary rounded-full animate-spin"></div>
        <span className="ml-3">Loading...</span>
      </div>
    );
  }

  // If not authenticated or no user data, don't render the children
  if (!isAuthenticated || !user) {
    return null;
  }

  return <>{children}</>;
};

export default AuthGuard;
