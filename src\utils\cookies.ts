// Cookie utility functions

// Cookie options
const DEFAULT_OPTIONS = {
  path: '/',
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
};

/**
 * Set a cookie
 * @param name Cookie name
 * @param value Cookie value
 * @param options Cookie options
 */
export const setCookie = (
  name: string,
  value: string,
  options: { expires?: number | Date } = {}
): void => {
  const cookieOptions = {
    ...DEFAULT_OPTIONS,
    ...options,
  };

  let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;

  if (cookieOptions.expires) {
    const expirationDate =
      typeof cookieOptions.expires === 'number'
        ? new Date(Date.now() + cookieOptions.expires * 1000 * 60 * 60 * 24)
        : cookieOptions.expires;

    cookieString += `; expires=${expirationDate.toUTCString()}`;
  }

  if (cookieOptions.path) {
    cookieString += `; path=${cookieOptions.path}`;
  }

  if (cookieOptions.secure) {
    cookieString += '; secure';
  }

  if (cookieOptions.sameSite) {
    cookieString += `; samesite=${cookieOptions.sameSite}`;
  }

  document.cookie = cookieString;
};

/**
 * Get a cookie by name
 * @param name Cookie name
 * @returns Cookie value or null if not found
 */
export const getCookie = (name: string): string | null => {
  const cookies = document.cookie.split(';');

  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();

    // Check if this cookie starts with the name we want
    if (cookie.startsWith(`${encodeURIComponent(name)}=`)) {
      return decodeURIComponent(
        cookie.substring(name.length + 1, cookie.length)
      );
    }
  }

  return null;
};

/**
 * Remove a cookie by name
 * @param name Cookie name
 */
export const removeCookie = (name: string): void => {
  // Set expiration to past date to remove the cookie
  setCookie(name, '', { expires: new Date(0) });
};

// Cookie names
export const COOKIE_NAMES = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  TOKEN_EXPIRES_AT: 'token_expires_at',
};
