# Debug Chat History Loading Issue

## Current Implementation Status

### ✅ What's Working:
1. WebSocket provider is integrated in App.tsx
2. socket.io-client dependency is installed
3. Environment variables are configured correctly
4. Chat page calls `fetchMessages(sessionId)` when session is selected
5. ChatWithStreaming component now uses Redux for messages

### 🔍 Potential Issues:

#### 1. API Endpoint Mismatch
- **chatApi.ts** uses: `/api/chat/sessions/{sessionId}/messages` (line 52)
- **ChatWithStreaming** was using: `/api/sessions/{sessionId}/messages` (FIXED)
- **Redux fetchMessages** uses: `chatApi.getMessages(sessionId)` (correct endpoint)

#### 2. Double Fetching
- Chat page calls `fetchMessages` when session is selected
- ChatWithStreaming component also calls `fetchMessages` in useEffect
- This could cause race conditions or conflicts

#### 3. Message Format Conversion
- API returns messages with `role` field
- Component expects `type` field
- Conversion is handled in ChatWithStreaming component

## 🔧 Debugging Steps

### Step 1: Check API Response
Test the API endpoint directly:
```bash
curl -X GET "http://localhost:8000/api/chat/sessions/{sessionId}/messages" \
  -H "Content-Type: application/json" \
  -b "your-auth-cookies"
```

Expected response:
```json
{
  "messages": [
    {
      "id": "msg-123",
      "role": "user",
      "content": "Hello!",
      "timestamp": "2025-05-24T15:33:37.873013",
      "metadata": {}
    }
  ]
}
```

### Step 2: Check Redux State
In browser console:
```javascript
// Check if messages are in Redux store
console.log(store.getState().chat.messages);

// Check if session is selected
console.log(store.getState().chat.currentChatSession);

// Check loading state
console.log(store.getState().chat.isLoading);
```

### Step 3: Check Network Tab
1. Open browser DevTools → Network tab
2. Select a chat session
3. Look for API calls to `/api/chat/sessions/{sessionId}/messages`
4. Check if the call succeeds and returns data

### Step 4: Check Console Logs
Look for these logs:
- "Failed to load messages for session: {sessionId}"
- "Error loading chat history:"
- Any 404 or 500 errors

## 🚀 Quick Fix Attempts

### Fix 1: Remove Duplicate Fetching
The ChatWithStreaming component should NOT fetch messages itself since the Chat page already does it.

### Fix 2: Ensure Correct API Endpoint
Make sure all API calls use `/api/chat/sessions/{sessionId}/messages`

### Fix 3: Check Authentication
Ensure cookies are being sent with requests

### Fix 4: Test with Simple Session
Create a new session, add a message, then switch to it to see if history loads

## 🎯 Expected Behavior

When selecting a chat session:
1. Chat page calls `fetchMessages(sessionId)`
2. Redux state updates with messages
3. ChatWithStreaming component reads from Redux
4. Messages display immediately
5. WebSocket connects for new streaming

## 🐛 Common Issues

1. **Backend not running**: Ensure backend is started with WebSocket support
2. **Wrong endpoint**: Backend might expect different endpoint format
3. **Authentication**: User might not be authenticated
4. **CORS**: Cross-origin issues if frontend/backend on different ports
5. **Empty sessions**: Session might not have any messages yet

## 🔍 Next Steps

1. Test API endpoint directly with curl
2. Check browser console for errors
3. Verify Redux state contains messages
4. Check if backend is returning correct data format
5. Test with a session that definitely has messages
