
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import ChatDemo from './ChatDemo';
import { Link } from 'react-router-dom';

const HeroSection: React.FC = () => {
  return (
    <section className="relative pt-32 pb-16 md:pt-40 md:pb-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
          <div className="flex-1 space-y-6">
            <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm text-primary animate-fade-in">
              Introducing AnyDocAI
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight animate-fade-up">
              Chat with <span className="gradient-text">all your documents</span> in one place
            </h1>
            <p className="text-lg text-muted-foreground max-w-xl animate-fade-up animate-delay-200">
              Ask questions, get summaries, extract data, and find insights across PDFs, Word docs, Excel sheets, and more — powered by GPT-4 Turbo and Claude 3.
            </p>
            <div className="flex flex-wrap gap-4 pt-2 animate-fade-up animate-delay-300">
              <Link to="/chat">
                <Button size="lg" className="gap-2 group">
                  Try for free 
                  <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
              <Button variant="outline" size="lg">
                View Demo
              </Button>
            </div>
            <div className="flex items-center gap-4 text-sm text-muted-foreground pt-2 animate-fade-up animate-delay-400">
              <div className="flex -space-x-2">
                <div className="w-7 h-7 rounded-full bg-docai-purple/20 flex items-center justify-center text-xs">★</div>
                <div className="w-7 h-7 rounded-full bg-docai-blue/20 flex items-center justify-center text-xs">★</div>
                <div className="w-7 h-7 rounded-full bg-docai-indigo/20 flex items-center justify-center text-xs">★</div>
              </div>
              <span>Trusted by 10000+ users worldwide</span>
            </div>
          </div>
          <div className="flex-1 w-full max-w-lg animate-fade-up animate-delay-300">
            <ChatDemo />
          </div>
        </div>
      </div>
      <div className="absolute inset-0 -z-10 h-full w-full bg-white bg-[radial-gradient(#8B5CF620_1px,transparent_1px)] [background-size:16px_16px]"></div>
    </section>
  );
};

export default HeroSection;
