import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent } from '@/components/ui/card';
import { useAppSelector } from '@/app/hooks';
import { selectUser } from '@/features/auth/authSlice';
import { authApi, TwoFactorVerify } from '@/services/api';

const TwoFactorAuthForm: React.FC = () => {
  const user = useAppSelector(selectUser);
  const { toast } = useToast();

  // State for 2FA setup
  const [is2FAEnabled, setIs2FAEnabled] = useState(false); // In a real app, this would come from the user object
  const [isSettingUp2FA, setIsSettingUp2FA] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isDisabling, setIsDisabling] = useState(false);
  
  // QR code and verification
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [backupCodes, setBackupCodes] = useState<string[] | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [disableCode, setDisableCode] = useState('');

  // Enable 2FA
  const handleEnable2FA = async () => {
    try {
      setIsSettingUp2FA(true);
      
      // Call the API to get QR code
      const response = await authApi.enable2FA();
      
      // In a real app, the response would contain the QR code image and backup codes
      // For now, we'll simulate it
      setQrCodeUrl('https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=otpauth://totp/AnyDocAI:' + user?.email);
      setBackupCodes(['12345678', '87654321', '11223344', '44332211', '55667788']);
      
    } catch (error) {
      toast({
        title: '2FA Setup Failed',
        description: error instanceof Error ? error.message : 'Failed to set up two-factor authentication.',
        variant: 'destructive',
      });
    } finally {
      setIsSettingUp2FA(false);
    }
  };

  // Verify 2FA setup
  const handleVerify2FA = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!verificationCode.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Please enter the verification code.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsVerifying(true);
      
      // Create verification data
      const verifyData: TwoFactorVerify = {
        code: verificationCode
      };
      
      // Call the API
      await authApi.verify2FA(verifyData);
      
      // Update state
      setIs2FAEnabled(true);
      setQrCodeUrl(null);
      setBackupCodes(null);
      setVerificationCode('');
      
      toast({
        title: '2FA Enabled',
        description: 'Two-factor authentication has been enabled for your account.',
      });
    } catch (error) {
      toast({
        title: 'Verification Failed',
        description: error instanceof Error ? error.message : 'Failed to verify the code. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsVerifying(false);
    }
  };

  // Disable 2FA
  const handleDisable2FA = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!disableCode.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Please enter the verification code to disable 2FA.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsDisabling(true);
      
      // Create verification data
      const verifyData: TwoFactorVerify = {
        code: disableCode
      };
      
      // Call the API
      await authApi.disable2FA(verifyData);
      
      // Update state
      setIs2FAEnabled(false);
      setDisableCode('');
      
      toast({
        title: '2FA Disabled',
        description: 'Two-factor authentication has been disabled for your account.',
      });
    } catch (error) {
      toast({
        title: 'Disable Failed',
        description: error instanceof Error ? error.message : 'Failed to disable 2FA. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDisabling(false);
    }
  };

  // If 2FA is not set up yet
  if (!is2FAEnabled && !qrCodeUrl) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertTitle>Two-factor authentication is not enabled</AlertTitle>
          <AlertDescription>
            Add an extra layer of security to your account by enabling two-factor authentication.
            You'll need an authenticator app like Google Authenticator or Authy.
          </AlertDescription>
        </Alert>
        
        <Button 
          onClick={handleEnable2FA} 
          disabled={isSettingUp2FA}
          className="w-full"
        >
          {isSettingUp2FA ? (
            <>
              <div className="h-4 w-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2" />
              Setting up...
            </>
          ) : (
            'Enable Two-Factor Authentication'
          )}
        </Button>
      </div>
    );
  }

  // If 2FA setup is in progress (showing QR code)
  if (!is2FAEnabled && qrCodeUrl) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertTitle>Scan the QR code</AlertTitle>
          <AlertDescription>
            Use an authenticator app to scan this QR code. Then enter the verification code below.
          </AlertDescription>
        </Alert>
        
        <div className="flex justify-center">
          <img src={qrCodeUrl} alt="QR Code" className="w-48 h-48" />
        </div>
        
        {backupCodes && (
          <Card>
            <CardContent className="pt-6">
              <h3 className="font-medium mb-2">Backup Codes</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Save these backup codes in a secure place. You can use them to access your account if you lose your authenticator device.
              </p>
              <div className="grid grid-cols-2 gap-2">
                {backupCodes.map((code, index) => (
                  <div key={index} className="font-mono text-sm bg-muted p-2 rounded">
                    {code}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
        
        <form onSubmit={handleVerify2FA} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="verificationCode">Verification Code</Label>
            <Input
              id="verificationCode"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              placeholder="Enter 6-digit code"
              maxLength={6}
              disabled={isVerifying}
              required
            />
          </div>
          
          <Button 
            type="submit" 
            className="w-full"
            disabled={isVerifying}
          >
            {isVerifying ? (
              <>
                <div className="h-4 w-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2" />
                Verifying...
              </>
            ) : (
              'Verify and Enable'
            )}
          </Button>
        </form>
      </div>
    );
  }

  // If 2FA is already enabled
  return (
    <div className="space-y-6">
      <Alert>
        <AlertTitle>Two-factor authentication is enabled</AlertTitle>
        <AlertDescription>
          Your account is protected with two-factor authentication. You'll need to enter a verification code from your authenticator app when you sign in.
        </AlertDescription>
      </Alert>
      
      <form onSubmit={handleDisable2FA} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="disableCode">Verification Code</Label>
          <Input
            id="disableCode"
            value={disableCode}
            onChange={(e) => setDisableCode(e.target.value)}
            placeholder="Enter 6-digit code"
            maxLength={6}
            disabled={isDisabling}
            required
          />
          <p className="text-xs text-muted-foreground">
            Enter a verification code from your authenticator app to disable two-factor authentication.
          </p>
        </div>
        
        <Button 
          type="submit" 
          variant="destructive"
          className="w-full"
          disabled={isDisabling}
        >
          {isDisabling ? (
            <>
              <div className="h-4 w-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2" />
              Disabling...
            </>
          ) : (
            'Disable Two-Factor Authentication'
          )}
        </Button>
      </form>
    </div>
  );
};

export default TwoFactorAuthForm;
