
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';

const useCases = [
  {
    title: "Legal Professionals",
    description: "Quickly analyze contracts, case law, and legal documents to extract key information and insights.",
    example: "Summarize this 40-page contract and highlight potential risks."
  },
  {
    title: "Researchers & Students",
    description: "Review academic papers, organize notes, and extract specific information from course materials.",
    example: "What methodology was used across these 3 research papers?"
  },
  {
    title: "Business Analysts",
    description: "Extract insights from financial statements, reports, and presentations to inform decision-making.",
    example: "Compare the financial metrics across these quarterly reports."
  },
  {
    title: "HR Teams",
    description: "Process resumes, policies, and employment documentation with improved efficiency.",
    example: "Find candidates with experience in machine learning from these resumes."
  }
];

const UseCasesSection: React.FC = () => {
  return (
    <section className="py-16 bg-muted/50">
      <div className="container px-4 md:px-6">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Who uses <span className="gradient-text">AnyDocAI</span>?
          </h2>
          <p className="text-muted-foreground">
            See how different professionals leverage AI to transform their document workflows.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {useCases.map((useCase, index) => (
            <Card key={index} className="border">
              <CardHeader>
                <h3 className="font-bold text-lg">{useCase.title}</h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">{useCase.description}</p>
                <div className="bg-primary/5 p-3 rounded-lg text-sm">
                  <span className="text-xs text-muted-foreground block mb-1">Example Query:</span>
                  "{useCase.example}"
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default UseCasesSection;
