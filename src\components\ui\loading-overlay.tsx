import React from 'react';
import { cn } from '@/lib/utils';
import { LoadingType } from '@/hooks/useLoadingAnimation';
import {
  PulseDotsLoader,
  SpinningRingLoader,
  BouncingBallsLoader,
  MorphingSquaresLoader,
  WaveLoader,
  RotatingHexagonLoader,
  TypingDotsLoader,
  ProgressRingLoader,
  FloatingParticlesLoader,
  DNAHelixLoader
} from './loading-animations';

interface LoadingOverlayProps {
  isVisible: boolean;
  type?: LoadingType;
  message?: string;
  progress?: number;
  className?: string;
  backdrop?: 'blur' | 'dark' | 'light';
  size?: 'sm' | 'md' | 'lg';
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  type = 'pulse-dots',
  message = 'Loading...',
  progress,
  className,
  backdrop = 'blur',
  size = 'md'
}) => {
  if (!isVisible) return null;

  const backdropClasses = {
    blur: 'backdrop-blur-sm bg-white/80',
    dark: 'bg-black/50',
    light: 'bg-white/90'
  };

  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  const getLoadingComponent = () => {
    const loaderProps = { className: "mb-4" };
    
    switch (type) {
      case 'pulse-dots':
        return <PulseDotsLoader {...loaderProps} size={size} />;
      case 'spinning-ring':
        return <SpinningRingLoader {...loaderProps} size={size === 'sm' ? 30 : size === 'lg' ? 60 : 40} />;
      case 'bouncing-balls':
        return <BouncingBallsLoader {...loaderProps} />;
      case 'morphing-squares':
        return <MorphingSquaresLoader {...loaderProps} />;
      case 'wave':
        return <WaveLoader {...loaderProps} />;
      case 'rotating-hexagon':
        return <RotatingHexagonLoader {...loaderProps} />;
      case 'typing-dots':
        return <TypingDotsLoader {...loaderProps} />;
      case 'progress-ring':
        return <ProgressRingLoader {...loaderProps} progress={progress} size={size === 'sm' ? 40 : size === 'lg' ? 80 : 60} />;
      case 'floating-particles':
        return <FloatingParticlesLoader {...loaderProps} />;
      case 'dna-helix':
        return <DNAHelixLoader {...loaderProps} />;
      default:
        return <PulseDotsLoader {...loaderProps} size={size} />;
    }
  };

  return (
    <div className={cn(
      "fixed inset-0 z-50 flex items-center justify-center transition-all duration-300",
      backdropClasses[backdrop],
      className
    )}>
      <div className="flex flex-col items-center justify-center p-8 rounded-lg bg-white shadow-lg border max-w-sm mx-4">
        {getLoadingComponent()}
        <p className={cn(
          "text-gray-700 font-medium text-center",
          sizeClasses[size]
        )}>
          {message}
        </p>
        {progress !== undefined && type !== 'progress-ring' && (
          <div className="w-full mt-4">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>Progress</span>
              <span>{progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-[#8B5CF6] to-[#3B82F6] h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Inline loading component for smaller areas
export const InlineLoader: React.FC<{
  type?: LoadingType;
  message?: string;
  progress?: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  direction?: 'horizontal' | 'vertical';
}> = ({
  type = 'pulse-dots',
  message,
  progress,
  className,
  size = 'sm',
  direction = 'horizontal'
}) => {
  const getLoadingComponent = () => {
    const loaderProps = { 
      className: direction === 'horizontal' ? "mr-2" : "mb-2",
      size: size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'md'
    };
    
    switch (type) {
      case 'pulse-dots':
        return <PulseDotsLoader {...loaderProps} />;
      case 'spinning-ring':
        return <SpinningRingLoader {...loaderProps} size={size === 'sm' ? 20 : size === 'lg' ? 32 : 24} />;
      case 'bouncing-balls':
        return <BouncingBallsLoader {...loaderProps} />;
      case 'morphing-squares':
        return <MorphingSquaresLoader {...loaderProps} />;
      case 'wave':
        return <WaveLoader {...loaderProps} />;
      case 'rotating-hexagon':
        return <RotatingHexagonLoader {...loaderProps} />;
      case 'typing-dots':
        return <TypingDotsLoader {...loaderProps} />;
      case 'progress-ring':
        return <ProgressRingLoader {...loaderProps} progress={progress} size={size === 'sm' ? 24 : size === 'lg' ? 40 : 32} />;
      case 'floating-particles':
        return <FloatingParticlesLoader {...loaderProps} />;
      case 'dna-helix':
        return <DNAHelixLoader {...loaderProps} />;
      default:
        return <PulseDotsLoader {...loaderProps} />;
    }
  };

  return (
    <div className={cn(
      "flex items-center",
      direction === 'vertical' ? 'flex-col' : 'flex-row',
      className
    )}>
      {getLoadingComponent()}
      {message && (
        <span className={cn(
          "text-gray-600",
          size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'
        )}>
          {message}
        </span>
      )}
    </div>
  );
};

// Button with integrated loading state
export const LoadingButton: React.FC<{
  isLoading: boolean;
  loadingType?: LoadingType;
  loadingText?: string;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
}> = ({
  isLoading,
  loadingType = 'spinning-ring',
  loadingText = 'Loading...',
  children,
  className,
  disabled,
  onClick,
  variant = 'primary'
}) => {
  const variantClasses = {
    primary: 'bg-[#8B5CF6] hover:bg-[#7C3AED] text-white',
    secondary: 'bg-gray-500 hover:bg-gray-600 text-white',
    outline: 'border border-[#8B5CF6] text-[#8B5CF6] hover:bg-[#8B5CF6] hover:text-white'
  };

  return (
    <button
      className={cn(
        "px-4 py-2 rounded-md font-medium transition-all duration-200 flex items-center justify-center min-w-[120px]",
        variantClasses[variant],
        (isLoading || disabled) && "opacity-70 cursor-not-allowed",
        className
      )}
      disabled={isLoading || disabled}
      onClick={onClick}
    >
      {isLoading ? (
        <InlineLoader 
          type={loadingType} 
          message={loadingText}
          size="sm"
          direction="horizontal"
          className="text-current"
        />
      ) : (
        children
      )}
    </button>
  );
};
