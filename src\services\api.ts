import { Document, ChatMessage, ChatSession } from '@/types/chat';

import axios from 'axios';

import { getC<PERSON><PERSON>, remove<PERSON><PERSON><PERSON>, COOKIE_NAMES } from '../utils/cookies';

// Base API URL
const API_URL = 'http://localhost:8000/api';

// Configure axios defaults
axios.defaults.baseURL = API_URL;
axios.defaults.withCredentials = true; // Include cookies in requests

// Add request interceptor to include auth token in headers
axios.interceptors.request.use(
  (config) => {
    const token = getCookie(COOKIE_NAMES.AUTH_TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle auth errors
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle authentication errors
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      // Only clear cookies for auth-related endpoints to avoid clearing during login/register
      const url = error.config.url;
      if (!url || (url && !url.includes('/login') && !url.includes('/register'))) {
        console.warn('Authentication error detected:', error.response.status);

        // Dispatch an event that can be listened to by the AuthGuard
        const authErrorEvent = new CustomEvent('auth-error', {
          detail: {
            status: error.response.status,
            message: error.response.data?.detail || 'Authentication failed'
          }
        });
        window.dispatchEvent(authErrorEvent);

        // Clear auth cookies
        removeCookie(COOKIE_NAMES.AUTH_TOKEN);
        removeCookie(COOKIE_NAMES.USER_DATA);
      }
    }
    return Promise.reject(error);
  }
);

// Type definitions for API requests
export interface UserCreate {
  email: string;
  password: string;
  full_name: string;
}

export interface UserLogin {
  email: string;
  password: string;
}

// Auth API functions
export const authApi = {
  register: (userData: UserCreate) =>
    axios.post('/auth/register', userData),

  login: (userData: UserLogin) =>
    axios.post('/auth/login', userData),

  logout: () =>
    axios.post('/auth/logout'),

  getCurrentUser: () =>
    axios.get('/auth/me'),

  updateProfile: (fullName: string, email: string) =>
    axios.put('/auth/update-profile', { full_name: fullName, email }),

  changePassword: (currentPassword: string, newPassword: string) =>
    axios.put('/auth/change-password', { current_password: currentPassword, new_password: newPassword }),

  enable2FA: () =>
    axios.post('/auth/enable-2fa'),

  verify2FA: (code: string) =>
    axios.post('/auth/verify-2fa', { code }),

  disable2FA: (code: string) =>
    axios.delete('/auth/disable-2fa', { data: { code } }),

  deleteAccount: (password: string) =>
    axios.delete('/auth/delete-account', { data: { password } })
};

// Document API functions
export const documentApi = {
  uploadDocumentToSession: (file: File, sessionId?: string) => {
    const formData = new FormData();
    formData.append('file', file);

    const url = sessionId
      ? `/documents/upload?session_id=${sessionId}`
      : '/documents/upload';

    return axios.post(url, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },

  listSessionDocuments: (sessionId: string) =>
    axios.get(`/chat/sessions/${sessionId}/documents`),

  getSessionDocumentDetails: (sessionId: string) =>
    axios.get(`/chat/sessions/${sessionId}/documents/details`),

  listAllDocuments: () =>
    axios.get('/documents/list'),

  getDocumentPreview: (documentId: string) =>
    axios.get(`/documents/${documentId}/preview`),

  deleteDocument: (documentId: string) =>
    axios.delete(`/documents/${documentId}`)
};

// Session API functions
export const sessionApi = {
  createSession: (name: string, documentIds: string[] = []) =>
    axios.post('/chat/sessions', { name, document_ids: documentIds }),

  listSessions: () =>
    axios.get('/chat/sessions'),

  getSession: (sessionId: string) =>
    axios.get(`/chat/sessions/${sessionId}`),

  updateSession: (sessionId: string, name: string) =>
    axios.put(`/chat/sessions/${sessionId}`, { name }),

  deleteSession: (sessionId: string) =>
    axios.delete(`/chat/sessions/${sessionId}`),

  addDocumentsToSession: (sessionId: string, documentIds: string[]) =>
    axios.put(`/chat/sessions/${sessionId}/documents`, { document_ids: documentIds }),

  removeDocumentFromSession: (sessionId: string, documentId: string) =>
    axios.delete(`/chat/sessions/${sessionId}/documents/${documentId}`)
};

// Type definitions for chat API
export interface ChatHistoryMessage {
  role: string;
  content: string;
}

export interface ChatRequest {
  message: string;
  chat_history?: ChatHistoryMessage[];
  use_agent?: boolean;
}

export interface ChatResponse {
  message: string;
  sources?: any[];
  metadata?: any;
}

// Chat API functions
export const chatApi = {
  getMessages: (sessionId: string) =>
    axios.get(`/chat/sessions/${sessionId}/messages`),

  sendMessage: (sessionId: string, request: ChatRequest): Promise<{ data: ChatResponse }> =>
    axios.post(`/chat/sessions/${sessionId}/messages`, request),

  getSuggestions: (sessionId: string) =>
    axios.get(`/chat/sessions/${sessionId}/suggestions`),

  // Legacy endpoint for backward compatibility
  sendLegacyMessage: (message: string, useAgent: boolean = false) =>
    axios.post('/chat/message', {
      message,
      use_agent: useAgent
    })
};

// Session storage utility
export const sessionStorageUtil = {
  // Current session data
  setCurrentSession: (sessionData: ChatSession) => {
    sessionStorage.setItem('currentSession', JSON.stringify(sessionData));
  },

  getCurrentSession: (): ChatSession | null => {
    const data = sessionStorage.getItem('currentSession');
    return data ? JSON.parse(data) : null;
  },

  // Messages cache
  cacheMessages: (sessionId: string, messages: ChatMessage[]) => {
    sessionStorage.setItem(`messages_${sessionId}`, JSON.stringify(messages));
  },

  getCachedMessages: (sessionId: string): ChatMessage[] => {
    const data = sessionStorage.getItem(`messages_${sessionId}`);
    return data ? JSON.parse(data) : [];
  },

  // Document metadata cache
  cacheDocumentMetadata: (documentId: string, metadata: Document) => {
    const cache = JSON.parse(sessionStorage.getItem('documentCache') || '{}');
    cache[documentId] = metadata;
    sessionStorage.setItem('documentCache', JSON.stringify(cache));
  },

  getCachedDocumentMetadata: (documentId: string): Document | null => {
    const cache = JSON.parse(sessionStorage.getItem('documentCache') || '{}');
    return cache[documentId] || null;
  }
};
