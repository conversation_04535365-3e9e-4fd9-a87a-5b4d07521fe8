import React from 'react';
import { Document } from '@/types/chat';
import { FileText, FileSpreadsheet, Presentation, FileImage, FileCode, Trash } from 'lucide-react';


interface DocumentCardProps {
  document: Document;
  isSelected: boolean;
  onDocumentView: (doc: Document) => void;
  onDocumentDelete?: (id: string) => void;
  showDeleteButton?: boolean;
}

const DocumentCard: React.FC<DocumentCardProps> = ({
  document,
  isSelected,
  onDocumentView,
  onDocumentDelete,
  showDeleteButton = true
}) => {
  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'xlsx':
      case 'csv':
        return <FileSpreadsheet className="h-4 w-4 text-green-600" />;
      case 'pptx':
      case 'ppt':
        return <Presentation className="h-4 w-4 text-orange-600" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <FileImage className="h-4 w-4 text-purple-600" />;
      case 'html':
      case 'json':
      case 'xml':
        return <FileCode className="h-4 w-4 text-yellow-600" />;
      default:
        return <FileText className="h-4 w-4 text-blue-600" />;
    }
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the parent click handler
    if (onDocumentDelete) {
      onDocumentDelete(document.id);
    }
  };

  return (
    <div
      className={`flex items-center gap-2 p-2 rounded-md cursor-pointer transition-colors group ${
        isSelected
          ? 'bg-[#EDE9FE] text-[#8B5CF6] border border-[#8B5CF6]/20'
          : 'hover:bg-muted'
      }`}
      onClick={() => onDocumentView(document)}
    >
      <div className="flex-shrink-0">
        {getFileIcon(document.type)}
      </div>
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium truncate">{document.name}</div>
        <div className="text-xs text-muted-foreground">{document.size}</div>
      </div>
      {showDeleteButton && onDocumentDelete && (
        <button
          className="flex-shrink-0 p-1 rounded-sm hover:bg-gray-200 opacity-0 group-hover:opacity-100 focus:opacity-100 transition-opacity"
          onClick={handleDeleteClick}
          aria-label="Delete document"
          type="button"
        >
          <Trash className="h-4 w-4 text-red-500" />
        </button>
      )}
    </div>
  );
};

export default DocumentCard;
