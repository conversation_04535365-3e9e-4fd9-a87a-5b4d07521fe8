/**
 * Utility functions for date and time formatting
 */
import * as React from 'react';

/**
 * Debug function to help identify timezone issues
 * @param dateString - The date string to debug
 * @returns Debug information about the date
 */
export const debugDate = (dateString: string) => {
  console.log('🔍 Date Debug Info:', {
    original: dateString,
    parsed: new Date(dateString),
    localString: new Date(dateString).toString(),
    isoString: new Date(dateString).toISOString(),
    userTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timezoneOffset: new Date().getTimezoneOffset(),
    now: new Date().toString()
  });
};

/**
 * Parses a date string and returns a Date object, handling timezone issues
 * @param dateString - The date string to parse
 * @returns Date object in local timezone
 */
export const parseDate = (dateString: string): Date => {
  if (!dateString) throw new Error('Invalid date string');

  // Handle common timestamp formats
  const trimmed = dateString.trim();

  // If it's a number (Unix timestamp), convert it
  if (/^\d+$/.test(trimmed)) {
    const timestamp = parseInt(trimmed);
    // Check if it's in seconds (10 digits) or milliseconds (13 digits)
    return new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp);
  }

  // If the string ends with 'Z', it's UTC
  if (trimmed.endsWith('Z')) {
    return new Date(trimmed);
  }

  // If it has timezone offset (+XX:XX or -XX:XX)
  if (trimmed.includes('+') || trimmed.match(/-\d{2}:\d{2}$/)) {
    return new Date(trimmed);
  }

  // If it's an ISO string without timezone (YYYY-MM-DDTHH:mm:ss)
  if (trimmed.includes('T')) {
    // For ISO strings without timezone, we need to be careful
    // If it's from an API, it might be UTC but without the 'Z'
    // Let's try both interpretations and see which makes more sense
    const asLocal = new Date(trimmed);
    const asUTC = new Date(trimmed + 'Z');

    // If the "UTC" interpretation gives us a time closer to now, use that
    const now = new Date();
    const localDiff = Math.abs(now.getTime() - asLocal.getTime());
    const utcDiff = Math.abs(now.getTime() - asUTC.getTime());

    // If the UTC interpretation is significantly closer to current time, use it
    if (utcDiff < localDiff / 2) {
      return asUTC;
    }

    return asLocal;
  }

  // For other formats, let JavaScript parse it normally
  return new Date(trimmed);
};

/**
 * Formats a date string to a relative time format (e.g., "2 minutes ago", "1 hour ago")
 * @param dateString - ISO date string or any valid date string
 * @returns Formatted relative time string
 */
export const formatRelativeTime = (dateString: string): string => {
  if (!dateString) return 'Unknown';

  try {
    const date = parseDate(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    // If the date is in the future or invalid, return a fallback
    if (diffInSeconds < 0 || isNaN(diffInSeconds)) {
      return formatAbsoluteTime(dateString);
    }

    // Less than 1 minute
    if (diffInSeconds < 60) {
      return diffInSeconds <= 5 ? 'Just now' : `${diffInSeconds}s ago`;
    }

    // Less than 1 hour
    if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}m ago`;
    }

    // Less than 1 day
    if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}h ago`;
    }

    // Less than 1 week
    if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days}d ago`;
    }

    // Less than 1 month (30 days)
    if (diffInSeconds < 2592000) {
      const weeks = Math.floor(diffInSeconds / 604800);
      return `${weeks}w ago`;
    }

    // Less than 1 year
    if (diffInSeconds < 31536000) {
      const months = Math.floor(diffInSeconds / 2592000);
      return `${months}mo ago`;
    }

    // More than 1 year
    const years = Math.floor(diffInSeconds / 31536000);
    return `${years}y ago`;

  } catch (error) {
    console.warn('Error formatting relative time:', error);
    return formatAbsoluteTime(dateString);
  }
};

/**
 * Formats a date string to an absolute time format (e.g., "Dec 15, 2:30 PM")
 * @param dateString - ISO date string or any valid date string
 * @returns Formatted absolute time string
 */
export const formatAbsoluteTime = (dateString: string): string => {
  if (!dateString) return 'Unknown';

  try {
    const date = parseDate(dateString);
    const now = new Date();

    // Check if it's today (compare local dates)
    const isToday = date.toDateString() === now.toDateString();

    // Check if it's yesterday
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();

    // Check if it's this week
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());
    startOfWeek.setHours(0, 0, 0, 0);
    const isThisWeek = date >= startOfWeek;

    // Check if it's this year
    const isThisYear = date.getFullYear() === now.getFullYear();

    // Use simple local time formatting (JavaScript handles timezone automatically)
    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    };

    if (isToday) {
      return `Today ${date.toLocaleTimeString(undefined, timeOptions)}`;
    }

    if (isYesterday) {
      return `Yesterday ${date.toLocaleTimeString(undefined, timeOptions)}`;
    }

    if (isThisWeek) {
      const dayOptions: Intl.DateTimeFormatOptions = { weekday: 'short' };
      return `${date.toLocaleDateString(undefined, dayOptions)} ${date.toLocaleTimeString(undefined, timeOptions)}`;
    }

    if (isThisYear) {
      const dateOptions: Intl.DateTimeFormatOptions = {
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      };
      return date.toLocaleDateString(undefined, dateOptions);
    }

    // Different year
    const fullDateOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    };
    return date.toLocaleDateString(undefined, fullDateOptions);

  } catch (error) {
    console.warn('Error formatting absolute time:', error);
    return 'Invalid date';
  }
};

/**
 * Formats a date string for tooltips with full date and time information
 * @param dateString - ISO date string or any valid date string
 * @returns Formatted full date and time string
 */
export const formatFullDateTime = (dateString: string): string => {
  if (!dateString) return 'Unknown';

  try {
    const date = parseDate(dateString);

    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
      timeZoneName: 'short'
    };
    return date.toLocaleDateString(undefined, options);
  } catch (error) {
    console.warn('Error formatting full date time:', error);
    return 'Invalid date';
  }
};

/**
 * Checks if a date string represents a valid date
 * @param dateString - Date string to validate
 * @returns True if valid, false otherwise
 */
export const isValidDate = (dateString: string): boolean => {
  if (!dateString) return false;
  try {
    const date = parseDate(dateString);
    return !isNaN(date.getTime());
  } catch {
    return false;
  }
};

/**
 * Gets a user-friendly format preference based on how recent the date is
 * @param dateString - ISO date string or any valid date string
 * @returns Object with relative and absolute time formats
 */
export const getTimeFormats = (dateString: string) => {
  return {
    relative: formatRelativeTime(dateString),
    absolute: formatAbsoluteTime(dateString),
    full: formatFullDateTime(dateString),
    isValid: isValidDate(dateString)
  };
};

/**
 * Hook for creating a live-updating relative time component
 * @param dateString - ISO date string
 * @param updateInterval - Update interval in milliseconds (default: 60000 = 1 minute)
 * @returns Current relative time string that updates automatically
 */
export const useLiveRelativeTime = (dateString: string, updateInterval: number = 60000): string => {
  const [relativeTime, setRelativeTime] = React.useState(() => formatRelativeTime(dateString));

  React.useEffect(() => {
    if (!isValidDate(dateString)) return;

    const updateTime = () => {
      setRelativeTime(formatRelativeTime(dateString));
    };

    // Update immediately
    updateTime();

    // Set up interval for updates
    const interval = setInterval(updateTime, updateInterval);

    return () => clearInterval(interval);
  }, [dateString, updateInterval]);

  return relativeTime;
};
