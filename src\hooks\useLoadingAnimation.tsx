import React, { createContext, useContext, useState, ReactNode, useCallback } from 'react';
import {
  PulseDotsLoader,
  SpinningRingLoader,
  BouncingBallsLoader,
  MorphingSquaresLoader,
  WaveLoader,
  RotatingHexagonLoader,
  <PERSON>pingDotsLoader,
  ProgressRingLoader,
  FloatingParticlesLoader,
  DNAHelixLoader
} from '@/components/ui/loading-animations';

export type LoadingType =
  | 'pulse-dots'
  | 'spinning-ring'
  | 'bouncing-balls'
  | 'morphing-squares'
  | 'wave'
  | 'rotating-hexagon'
  | 'typing-dots'
  | 'progress-ring'
  | 'floating-particles'
  | 'dna-helix';

export interface LoadingState {
  isLoading: boolean;
  type: LoadingType;
  message?: string;
  progress?: number;
}

interface LoadingContextType {
  loadingStates: Record<string, LoadingState>;
  setLoading: (key: string, state: Partial<LoadingState>) => void;
  clearLoading: (key: string) => void;
  getLoadingComponent: (key: string, className?: string) => React.ReactNode;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const LoadingProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [loadingStates, setLoadingStates] = useState<Record<string, LoadingState>>({});

  const setLoading = useCallback((key: string, state: Partial<LoadingState>) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: {
        isLoading: true,
        type: 'pulse-dots',
        ...prev[key],
        ...state
      }
    }));
  }, []);

  const clearLoading = useCallback((key: string) => {
    setLoadingStates(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  }, []);

  const getLoadingComponent = useCallback((key: string, className?: string): React.ReactNode => {
    const state = loadingStates[key];
    if (!state?.isLoading) return null;

    const props = { className };

    switch (state.type) {
      case 'pulse-dots':
        return <PulseDotsLoader {...props} />;
      case 'spinning-ring':
        return <SpinningRingLoader {...props} />;
      case 'bouncing-balls':
        return <BouncingBallsLoader {...props} />;
      case 'morphing-squares':
        return <MorphingSquaresLoader {...props} />;
      case 'wave':
        return <WaveLoader {...props} />;
      case 'rotating-hexagon':
        return <RotatingHexagonLoader {...props} />;
      case 'typing-dots':
        return <TypingDotsLoader {...props} />;
      case 'progress-ring':
        return <ProgressRingLoader {...props} progress={state.progress} />;
      case 'floating-particles':
        return <FloatingParticlesLoader {...props} />;
      case 'dna-helix':
        return <DNAHelixLoader {...props} />;
      default:
        return <PulseDotsLoader {...props} />;
    }
  }, [loadingStates]);

  return (
    <LoadingContext.Provider value={{
      loadingStates,
      setLoading,
      clearLoading,
      getLoadingComponent
    }}>
      {children}
    </LoadingContext.Provider>
  );
};

export const useLoadingAnimation = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoadingAnimation must be used within a LoadingProvider');
  }
  return context;
};

// Predefined loading configurations for different API calls
export const LOADING_CONFIGS = {
  // Chat related
  FETCH_SESSIONS: { type: 'floating-particles' as LoadingType, message: 'Loading chat sessions...' },
  CREATE_SESSION: { type: 'rotating-hexagon' as LoadingType, message: 'Creating new session...' },
  DELETE_SESSION: { type: 'morphing-squares' as LoadingType, message: 'Deleting session...' },
  RENAME_SESSION: { type: 'pulse-dots' as LoadingType, message: 'Renaming session...' },

  // Document related
  FETCH_DOCUMENTS: { type: 'wave' as LoadingType, message: 'Loading documents...' },
  UPLOAD_DOCUMENT: { type: 'progress-ring' as LoadingType, message: 'Uploading document...' },
  DELETE_DOCUMENT: { type: 'spinning-ring' as LoadingType, message: 'Removing document...' },
  PROCESS_DOCUMENT: { type: 'dna-helix' as LoadingType, message: 'Processing document...' },

  // Message related
  SEND_MESSAGE: { type: 'bouncing-balls' as LoadingType, message: 'Sending message...' },
  FETCH_MESSAGES: { type: 'pulse-dots' as LoadingType, message: 'Loading messages...' },
  AI_TYPING: { type: 'typing-dots' as LoadingType, message: 'AI is thinking...' },

  // Auth related
  LOGIN: { type: 'spinning-ring' as LoadingType, message: 'Signing in...' },
  REGISTER: { type: 'floating-particles' as LoadingType, message: 'Creating account...' },
  LOGOUT: { type: 'pulse-dots' as LoadingType, message: 'Signing out...' },

  // General
  LOADING: { type: 'pulse-dots' as LoadingType, message: 'Loading...' },
  PROCESSING: { type: 'dna-helix' as LoadingType, message: 'Processing...' },
  SAVING: { type: 'morphing-squares' as LoadingType, message: 'Saving...' }
};

// Hook for easy API loading management
export const useApiLoading = () => {
  const { setLoading, clearLoading, getLoadingComponent, loadingStates } = useLoadingAnimation();

  const startLoading = useCallback((key: keyof typeof LOADING_CONFIGS, progress?: number) => {
    const config = LOADING_CONFIGS[key];
    setLoading(key, { ...config, progress });
  }, [setLoading]);

  const stopLoading = useCallback((key: keyof typeof LOADING_CONFIGS) => {
    clearLoading(key);
  }, [clearLoading]);

  const updateProgress = useCallback((key: keyof typeof LOADING_CONFIGS, progress: number) => {
    const currentState = loadingStates[key];
    if (currentState) {
      setLoading(key, { ...currentState, progress });
    }
  }, [loadingStates, setLoading]);

  const isLoading = useCallback((key: keyof typeof LOADING_CONFIGS): boolean => {
    return loadingStates[key]?.isLoading || false;
  }, [loadingStates]);

  const getLoader = useCallback((key: keyof typeof LOADING_CONFIGS, className?: string) => {
    return getLoadingComponent(key, className);
  }, [getLoadingComponent]);

  return {
    startLoading,
    stopLoading,
    updateProgress,
    isLoading,
    getLoader
  };
};
