
import { Button } from '@/components/ui/button';
import Navbar from '@/components/Navbar';
import { useAppSelector, useAppDispatch } from '@/app/hooks';
import { selectUser, selectIsAuthenticated, getCurrentUser } from '@/features/auth/authSlice';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import PasswordChangeForm from '@/components/settings/PasswordChangeForm';

import React, { useEffect, useState } from 'react';

import { useNavigate } from 'react-router-dom';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Try to get the current user when the component mounts
    const checkAuth = async () => {
      try {
        setIsLoading(true);
        await dispatch(getCurrentUser()).unwrap();
      } catch (error) {
        console.error('Authentication check failed:', error);
        navigate('/auth');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [dispatch, navigate]);

  useEffect(() => {
    // If authentication state changes to not authenticated, redirect to login
    if (!isLoading && !isAuthenticated) {
      navigate('/auth');
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="h-8 w-8 border-4 border-t-transparent border-primary rounded-full animate-spin"></div>
        <span className="ml-3">Loading...</span>
      </div>
    );
  }

  // If not authenticated or no user data, don't render the dashboard
  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="container mx-auto p-6 flex-1 mt-16">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-bold mb-6">Your Dashboard</h1>

          <div className="grid gap-6 md:grid-cols-2">
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h2 className="text-xl font-semibold mb-4">Account Information</h2>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground">Name</p>
                  <p className="font-medium">{user?.name || user?.full_name || 'User'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Email</p>
                  <p className="font-medium">{user?.email || 'No email provided'}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h2 className="text-xl font-semibold mb-4">Current Plan</h2>
              <div className="space-y-3 mb-4">
                <div>
                  <p className="text-sm text-muted-foreground">Plan</p>
                  <p className="font-medium">{user?.plan || user?.subscription_tier || 'Free'}</p>
                </div>
                {(!user?.plan || user?.plan === 'Free' || user?.plan === 'Basic') && (
                  <div className="text-sm text-muted-foreground">
                    <p>Includes:</p>
                    <ul className="list-disc pl-5 mt-1 space-y-1">
                      <li>10 document uploads per month</li>
                      <li>Up to 50 pages per document</li>
                      <li>Basic chat features</li>
                    </ul>
                  </div>
                )}
              </div>
              <Button
                className="w-full bg-[#8B5CF6] hover:bg-[#7C3AED]"
                onClick={() => alert('Upgrade functionality would go here')}
              >
                Upgrade Now
              </Button>
            </div>
          </div>

          <div className="mt-8 bg-white p-6 rounded-xl shadow-sm border">
            <h2 className="text-xl font-semibold mb-4">Account Security</h2>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">Manage your account security settings</p>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-md font-medium">Password</h3>
                  <p className="text-sm text-muted-foreground">Change your account password</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsPasswordDialogOpen(true)}
                >
                  Change Password
                </Button>
              </div>
            </div>
          </div>

          <div className="mt-8 bg-white p-6 rounded-xl shadow-sm border">
            <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
            <div className="text-center py-8 text-muted-foreground">
              <p>No recent activities found.</p>
              <Button
                variant="link"
                className="text-[#8B5CF6] mt-2"
                onClick={() => navigate('/chat')}
              >
                Start a new chat
              </Button>
            </div>
          </div>
        </div>
      </main>

      {/* Password Change Dialog */}
      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Change Password</DialogTitle>
            <DialogDescription>
              Update your password to keep your account secure
            </DialogDescription>
          </DialogHeader>
          <PasswordChangeForm onSuccess={() => setIsPasswordDialogOpen(false)} />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Dashboard;
