
import React from 'react';
import { Document } from '@/types/chat';
import { Check, FileText, FolderOpen } from 'lucide-react';
import DocumentCard from './DocumentCard';
import { InlineLoader } from '@/components/ui/loading-overlay';

interface DocumentListProps {
  documents: Document[];
  selectedDocuments: string[];
  currentDocumentId?: string;
  onDocumentSelect?: (id: string) => void;
  onDocumentView?: (doc: Document) => void;
  onDocumentDelete?: (id: string) => void;
  isViewMode?: boolean;
  isCollapsible?: boolean;
  isLoading?: boolean;
  showEmptyState?: boolean;
}

const DocumentList: React.FC<DocumentListProps> = ({
  documents,
  selectedDocuments,
  currentDocumentId,
  onDocumentSelect,
  onDocumentView,
  onDocumentDelete,
  isViewMode = false,
  isCollapsible = false,
  isLoading = false,
  showEmptyState = true
}) => {
  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-4">
        <InlineLoader
          type="wave"
          message="Loading documents..."
          size="sm"
          direction="vertical"
        />
      </div>
    );
  }

  // Handle empty state
  if (documents.length === 0) {
    if (isCollapsible) {
      return null;
    }

    if (showEmptyState) {
      return (
        <div className="flex flex-col items-center justify-center py-6 text-center">
          <FolderOpen className="h-8 w-8 text-gray-400 mb-2" />
          <p className="text-sm text-gray-500 mb-1">No documents found</p>
          <p className="text-xs text-gray-400">
            {isViewMode ? 'This session has no documents yet' : 'Upload documents to get started'}
          </p>
        </div>
      );
    }

    return null;
  }

  return (
    <div className="space-y-1">
      {documents.map(doc => {
        if (isViewMode) {
          // In view mode, use DocumentCard with current document highlighting
          return (
            <DocumentCard
              key={doc.id}
              document={doc}
              isSelected={currentDocumentId === doc.id}
              onDocumentView={onDocumentView!}
              onDocumentDelete={onDocumentDelete}
              showDeleteButton={true}
            />
          );
        } else {
          // In selection mode, use DocumentCard with selection highlighting
          return (
            <div key={doc.id} className="flex items-center gap-2">
              <DocumentCard
                document={doc}
                isSelected={selectedDocuments.includes(doc.id)}
                onDocumentView={(doc) => onDocumentSelect?.(doc.id)}
                showDeleteButton={false}
              />
              {selectedDocuments.includes(doc.id) && (
                <div className="flex-shrink-0">
                  <Check className="h-4 w-4" />
                </div>
              )}
            </div>
          );
        }
      })}
    </div>
  );
};

export default DocumentList;
