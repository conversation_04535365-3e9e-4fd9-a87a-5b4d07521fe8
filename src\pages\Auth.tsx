
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import Navbar from '@/components/Navbar';
import { useAppDispatch, useAppSelector } from '@/app/hooks';
import { login, register, selectIsAuthenticated, selectIsLoading, selectError } from '@/features/auth/authSlice';

const Auth: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { toast } = useToast();

  // Get auth state from Redux
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isLoading = useAppSelector(selectIsLoading);
  const error = useAppSelector(selectError);

  // Form state
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');
  const [signupName, setSignupName] = useState('');
  const [signupEmail, setSignupEmail] = useState('');
  const [signupPassword, setSignupPassword] = useState('');

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/chat');
    }
  }, [isAuthenticated, navigate]);

  // Show error toast if there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: 'Authentication Error',
        description: error,
        variant: 'destructive',
      });
    }
  }, [error, toast]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // Dispatch login action
    await dispatch(login({
      email: loginEmail,
      password: loginPassword
    }));
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();

    // Dispatch register action
    await dispatch(register({
      email: signupEmail,
      password: signupPassword,
      full_name: signupName
    }));
  };

  const handleGoogleAuth = () => {
    // In a real app, this would integrate with Google OAuth
    toast({
      title: 'Google Authentication',
      description: 'Google authentication is not implemented in this demo.',
    });
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md space-y-6 bg-white p-8 rounded-xl shadow-lg">
          <div className="text-center space-y-2">
            <h1 className="text-3xl font-bold">Welcome to AnyDocAI</h1>
            <p className="text-muted-foreground">Chat with your documents instantly</p>
          </div>

          <Tabs defaultValue="login" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="login">Login</TabsTrigger>
              <TabsTrigger value="signup">Sign Up</TabsTrigger>
            </TabsList>

            <TabsContent value="login" className="space-y-4 pt-4">
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="login-email">Email</Label>
                  <Input
                    id="login-email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                    value={loginEmail}
                    onChange={(e) => setLoginEmail(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="login-password">Password</Label>
                  <Input
                    id="login-password"
                    type="password"
                    placeholder="••••••••"
                    required
                    value={loginPassword}
                    onChange={(e) => setLoginPassword(e.target.value)}
                  />
                </div>
                <Button
                  type="submit"
                  className="w-full bg-[#8B5CF6] hover:bg-[#7C3AED]"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="h-4 w-4 border-2 border-t-transparent border-primary-foreground rounded-full animate-spin mr-2" />
                  ) : null}
                  Login
                </Button>
              </form>

              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-muted-foreground">Or continue with</span>
                </div>
              </div>

              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={handleGoogleAuth}
                disabled={isLoading}
              >
                <svg width="16" height="16" viewBox="0 0 16 16" className="mr-2">
                  <g transform="matrix(1, 0, 0, 1, 0, 0)">
                    <path d="M14.76,8.64a.79.79,0,0,0-.76-.64h-7l.26-1H12a.8.8,0,0,0,.77-.6l1-3A.8.8,0,0,0,13,2H4.59l-.49-1.67A.8.8,0,0,0,3.33,0h-2a.8.8,0,1,0,0,1.6H2.62L5.07,13h0L4,14.2a.8.8,0,0,0,.39,1.07.76.76,0,0,0,.34.08.79.79,0,0,0,.73-.47L7,12.21h5.48l1.64,2.53a.8.8,0,0,0,1.11.24.81.81,0,0,0,.24-1.12ZM5.68,11.11,4.4,5.6h7.25L10.66,8H8a.8.8,0,0,0-.77.6Z" fill="#4285F4" />
                  </g>
                </svg>
                Continue with Google
              </Button>
            </TabsContent>

            <TabsContent value="signup" className="space-y-4 pt-4">
              <form onSubmit={handleSignup} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="signup-name">Full Name</Label>
                  <Input
                    id="signup-name"
                    type="text"
                    placeholder="Your Name"
                    required
                    value={signupName}
                    onChange={(e) => setSignupName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="signup-email">Email</Label>
                  <Input
                    id="signup-email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                    value={signupEmail}
                    onChange={(e) => setSignupEmail(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="signup-password">Password</Label>
                  <Input
                    id="signup-password"
                    type="password"
                    placeholder="••••••••"
                    required
                    value={signupPassword}
                    onChange={(e) => setSignupPassword(e.target.value)}
                  />
                </div>
                <Button
                  type="submit"
                  className="w-full bg-[#8B5CF6] hover:bg-[#7C3AED]"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="h-4 w-4 border-2 border-t-transparent border-primary-foreground rounded-full animate-spin mr-2" />
                  ) : null}
                  Create Account
                </Button>
              </form>

              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-muted-foreground">Or continue with</span>
                </div>
              </div>

              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={handleGoogleAuth}
                disabled={isLoading}
              >
                <svg width="16" height="16" viewBox="0 0 16 16" className="mr-2">
                  <g transform="matrix(1, 0, 0, 1, 0, 0)">
                    <path d="M14.76,8.64a.79.79,0,0,0-.76-.64h-7l.26-1H12a.8.8,0,0,0,.77-.6l1-3A.8.8,0,0,0,13,2H4.59l-.49-1.67A.8.8,0,0,0,3.33,0h-2a.8.8,0,1,0,0,1.6H2.62L5.07,13h0L4,14.2a.8.8,0,0,0,.39,1.07.76.76,0,0,0,.34.08.79.79,0,0,0,.73-.47L7,12.21h5.48l1.64,2.53a.8.8,0,0,0,1.11.24.81.81,0,0,0,.24-1.12ZM5.68,11.11,4.4,5.6h7.25L10.66,8H8a.8.8,0,0,0-.77.6Z" fill="#4285F4" />
                  </g>
                </svg>
                Continue with Google
              </Button>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Auth;
