import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

import { RootState } from '../../app/store';
import { authApi, UserLogin, UserCreate } from '../../services/api';
import { setCookie, getC<PERSON>ie, remove<PERSON><PERSON><PERSON>, COOKIE_NAMES } from '../../utils/cookies';

// Define user type
export interface User {
  id?: string;
  name?: string;
  email: string;
  full_name?: string;
  subscription_tier?: string;
  plan?: string;
}

// Define auth state
export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  tokenExpiresAt: number | null;
}

// Get user data from cookies if available
const getUserFromCookies = (): User | null => {
  const userStr = getCookie(COOKIE_NAMES.USER_DATA);
  if (userStr) {
    try {
      return JSON.parse(userStr);
    } catch (e) {
      return null;
    }
  }
  return null;
};

// Get token from cookies if available
const getTokenFromCookies = (): string | null => {
  return getCookie(COOKIE_NAMES.AUTH_TOKEN);
};

// Get authentication status based on token existence
const getAuthStatusFromCookies = (): boolean => {
  return !!getCookie(COOKIE_NAMES.AUTH_TOKEN);
};

// Get token expiration from cookies if available
const getTokenExpirationFromCookies = (): number | null => {
  const expiresAtStr = getCookie(COOKIE_NAMES.TOKEN_EXPIRES_AT);
  return expiresAtStr ? parseInt(expiresAtStr, 10) : null;
};

// Initial state
const initialState: AuthState = {
  user: getUserFromCookies(),
  token: getTokenFromCookies(),
  isAuthenticated: getAuthStatusFromCookies(),
  isLoading: false,
  error: null,
  tokenExpiresAt: getTokenExpirationFromCookies(),
};

// Login thunk
export const login = createAsyncThunk(
  'auth/login',
  async (credentials: UserLogin, { rejectWithValue }) => {
    try {
      // Call the login API
      const response = await authApi.login(credentials);

      // Calculate token expiration (default to 24 hours if not provided)
      const expiresIn = response.data.expires_in || 86400; // seconds (24 hours)
      const expiresAt = Date.now() + expiresIn * 1000;

      // Store auth data in cookies
      if (response.data && response.data.user) {
        setCookie(COOKIE_NAMES.USER_DATA, JSON.stringify(response.data.user), { expires: 7 });
      }

      if (response.data.access_token) {
        setCookie(COOKIE_NAMES.AUTH_TOKEN, response.data.access_token, { expires: 7 });
        setCookie(COOKIE_NAMES.TOKEN_EXPIRES_AT, expiresAt.toString(), { expires: 7 });
      }

      return {
        user: response.data.user,
        token: response.data.access_token,
        tokenExpiresAt: expiresAt
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Login failed. Please check your credentials.');
    }
  }
);

// Register thunk
export const register = createAsyncThunk(
  'auth/register',
  async (userData: UserCreate, { rejectWithValue }) => {
    try {
      // Call the register API
      const response = await authApi.register(userData);

      // Calculate token expiration (default to 24 hours if not provided)
      const expiresIn = response.data.expires_in || 86400; // seconds (24 hours)
      const expiresAt = Date.now() + expiresIn * 1000;

      // Store auth data in cookies
      if (response.data && response.data.user) {
        setCookie(COOKIE_NAMES.USER_DATA, JSON.stringify(response.data.user), { expires: 7 });
      }

      if (response.data.access_token) {
        setCookie(COOKIE_NAMES.AUTH_TOKEN, response.data.access_token, { expires: 7 });
        setCookie(COOKIE_NAMES.TOKEN_EXPIRES_AT, expiresAt.toString(), { expires: 7 });
      }

      return {
        user: response.data.user,
        token: response.data.access_token,
        tokenExpiresAt: expiresAt
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Registration failed. Please try again.');
    }
  }
);

// Logout thunk
export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      // Call the logout API
      await authApi.logout();

      // Also clear our local cookies
      removeCookie(COOKIE_NAMES.USER_DATA);
      removeCookie(COOKIE_NAMES.AUTH_TOKEN);
      removeCookie(COOKIE_NAMES.TOKEN_EXPIRES_AT);

      return null;
    } catch (error) {
      // Even if the API call fails, we still want to clear the cookies
      removeCookie(COOKIE_NAMES.USER_DATA);
      removeCookie(COOKIE_NAMES.AUTH_TOKEN);
      removeCookie(COOKIE_NAMES.TOKEN_EXPIRES_AT);

      return rejectWithValue(error instanceof Error ? error.message : 'Logout failed.');
    }
  }
);

// Get current user thunk
export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue }) => {
    try {
      // Check if we have cookie data first
      const userStr = getCookie(COOKIE_NAMES.USER_DATA);
      const token = getCookie(COOKIE_NAMES.AUTH_TOKEN);
      const tokenExpiresAtStr = getCookie(COOKIE_NAMES.TOKEN_EXPIRES_AT);
      const tokenExpiresAt = tokenExpiresAtStr ? parseInt(tokenExpiresAtStr, 10) : null;

      let user = null;

      // If we have user data in cookies, parse it
      if (userStr) {
        try {
          user = JSON.parse(userStr);
          console.log('Found user data in cookies:', user);
        } catch (e) {
          console.error('Error parsing user data from cookies:', e);
        }
      }

      // Check if token is expired
      if (tokenExpiresAt && Date.now() > tokenExpiresAt) {
        console.warn('Token has expired, clearing auth state');
        removeCookie(COOKIE_NAMES.USER_DATA);
        removeCookie(COOKIE_NAMES.AUTH_TOKEN);
        removeCookie(COOKIE_NAMES.TOKEN_EXPIRES_AT);
        throw new Error('Your session has expired. Please log in again.');
      }

      // Try to get user from API if we have a token
      if (token) {
        try {
          console.log('Attempting to get user data from API with token');
          const response = await authApi.getCurrentUser();

          // Update our local cookie with the latest user data
          if (response.data) {
            console.log('Successfully retrieved user data from API:', response.data);
            setCookie(COOKIE_NAMES.USER_DATA, JSON.stringify(response.data), { expires: 7 });

            return {
              user: response.data,
              token: token,
              tokenExpiresAt: tokenExpiresAt
            };
          }
        } catch (apiError) {
          console.warn('API request failed:', apiError);
          // If API call fails but we have user data in cookies and token is not expired, use that
          if (user && tokenExpiresAt && Date.now() < tokenExpiresAt) {
            console.log('Using cached user data from cookies after API failure');
            return { user, token, tokenExpiresAt };
          }

          // If API call fails and we don't have user data, clear cookies and reject
          console.warn('No valid user data available, clearing auth state');
          removeCookie(COOKIE_NAMES.USER_DATA);
          removeCookie(COOKIE_NAMES.AUTH_TOKEN);
          removeCookie(COOKIE_NAMES.TOKEN_EXPIRES_AT);
          throw new Error('Authentication failed');
        }
      }

      // If we have user data from cookies but API call didn't succeed
      if (user && tokenExpiresAt && Date.now() < tokenExpiresAt) {
        console.log('Using user data from cookies:', user);
        return { user, token: token || 'valid-token', tokenExpiresAt };
      }

      // If we don't have user data or token, authentication has failed
      throw new Error('No authenticated user found');
    } catch (error) {
      console.error('getCurrentUser error:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to get current user.');
    }
  }
);

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Additional reducers if needed
    setCredentials: (state, action: PayloadAction<{ user: User; token: string; tokenExpiresAt?: number }>) => {
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.isAuthenticated = true;
      state.tokenExpiresAt = action.payload.tokenExpiresAt || null;
    },
    clearCredentials: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.tokenExpiresAt = null;
    },
    // Check if token is expired
    checkTokenExpiration: (state) => {
      if (state.tokenExpiresAt && Date.now() > state.tokenExpiresAt) {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.tokenExpiresAt = null;

        // Clear cookies
        removeCookie(COOKIE_NAMES.USER_DATA);
        removeCookie(COOKIE_NAMES.AUTH_TOKEN);
        removeCookie(COOKIE_NAMES.TOKEN_EXPIRES_AT);
      }
    },
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.tokenExpiresAt = action.payload.tokenExpiresAt;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Register
    builder
      .addCase(register.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.tokenExpiresAt = action.payload.tokenExpiresAt;
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Logout
    builder
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.tokenExpiresAt = null;
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        // Even on error, we want to clear the auth state
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.tokenExpiresAt = null;
      });

    // Get current user
    builder
      .addCase(getCurrentUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.tokenExpiresAt = action.payload.tokenExpiresAt;
      })
      .addCase(getCurrentUser.rejected, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.tokenExpiresAt = null;
      });
  },
});

// Export actions
export const { setCredentials, clearCredentials, checkTokenExpiration } = authSlice.actions;

// Export selectors
export const selectAuth = (state: RootState) => state.auth;
export const selectUser = (state: RootState) => state.auth.user;
export const selectIsAuthenticated = (state: RootState) => state.auth.isAuthenticated;
export const selectIsLoading = (state: RootState) => state.auth.isLoading;
export const selectError = (state: RootState) => state.auth.error;

// Export reducer
export default authSlice.reducer;
