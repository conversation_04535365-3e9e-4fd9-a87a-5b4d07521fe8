import React from 'react';
import { Document } from '@/types/chat';
import { FileText } from 'lucide-react';

interface DocumentViewerProps {
  document: Document;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({ document }) => {

  // Get document type and determine how to display it
  const getDocumentType = (filename: string) => {
    const extension = filename.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'docx':
      case 'doc':
        return 'word';
      case 'xlsx':
      case 'xls':
        return 'excel';
      case 'pptx':
      case 'ppt':
        return 'powerpoint';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return 'image';
      case 'txt':
        return 'text';
      default:
        return 'other';
    }
  };

  const documentType = getDocumentType(document.name);

  // If no URL is provided, show a message
  if (!document.url) {
    return (
      <div className="flex-1 flex items-center justify-center p-4 text-muted-foreground">
        <div className="text-center">
          <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>Document URL not available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Document Controls */}
      {/* <div className="flex items-center justify-between p-2 border-b bg-gray-50">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleZoomOut}
            disabled={zoom <= 25}
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
          <span className="text-sm font-medium min-w-[60px] text-center">{zoom}%</span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleZoomIn}
            disabled={zoom >= 300}
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRotate}
          >
            <RotateCw className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
          >
            <Download className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={toggleFullscreen}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        </div>
      </div> */}

      {/* Document Content */}
      <div className="flex-1 overflow-hidden bg-gray-100">
        <div className="w-full h-full flex items-center justify-center">
          {documentType === 'pdf' ? (
            <object
              data={document.url}
              type="application/pdf"
              className="w-full h-full max-w-full max-h-full"
              style={{ minHeight: '600px' }}
            >
              <embed
                src={document.url}
                type="application/pdf"
                className="w-full h-full"
                style={{ minHeight: '600px' }}
              />
            </object>
          ) : documentType === 'image' ? (
            <img
              src={document.url}
              alt={document.name}
              className="max-w-full max-h-full object-contain"
              style={{ maxHeight: '90vh' }}
            />
          ) : documentType === 'text' ? (
            <iframe
              src={document.url}
              className="w-full h-full border-0 bg-white"
              style={{ minHeight: '600px' }}
              title={document.name}
            />
          ) : (
            // For Word, Excel, PowerPoint and other documents, use Office Online viewer
            <iframe
              src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(document.url)}`}
              className="w-full h-full border-0"
              style={{ minHeight: '600px' }}
              title={document.name}
              sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentViewer;
