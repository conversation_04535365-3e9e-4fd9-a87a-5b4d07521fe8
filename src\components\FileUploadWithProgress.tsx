import React, { useState, useEffect, useCallback } from 'react';
import { useWebSocketContext } from '../contexts/WebSocketContext';
import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Upload, File, CheckCircle, XCircle, Clock } from 'lucide-react';
import { toast } from 'sonner';

interface UploadingFile {
  file: File;
  status: 'uploading' | 'processing' | 'processed' | 'error';
  progress: number;
  actualFileId?: string;
  error?: string;
  processingStage?: string;
  processingProgress?: number;
  processingMessage?: string;
  metadata?: Record<string, any>;
}

interface FileUploadWithProgressProps {
  onUploadComplete?: (fileId: string, fileStatus: any) => void;
  sessionId?: string;
  accept?: string;
  multiple?: boolean;
}

const FileUploadWithProgress: React.FC<FileUploadWithProgressProps> = ({
  onUploadComplete,
  sessionId,
  accept = ".pdf,.docx,.pptx,.xlsx,.txt",
  multiple = true
}) => {
  const [uploadingFiles, setUploadingFiles] = useState<Record<string, UploadingFile>>({});
  const webSocket = useWebSocketContext();

  const handleFileSelect = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files);

    for (const file of fileArray) {
      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      setUploadingFiles(prev => ({
        ...prev,
        [tempId]: {
          file,
          status: 'uploading',
          progress: 0
        }
      }));

      try {
        const formData = new FormData();
        formData.append('file', file);
        if (sessionId) formData.append('session_id', sessionId);

        const response = await fetch(`${import.meta.env.VITE_API_URL || process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/documents/upload`, {
          method: 'POST',
          body: formData,
          credentials: 'include'
        });

        if (!response.ok) throw new Error(`Upload failed: ${response.statusText}`);

        const result = await response.json();
        const actualFileId = result.file_id;

        // Update with actual file ID and join WebSocket room
        setUploadingFiles(prev => {
          const updated = { ...prev };
          updated[actualFileId] = {
            ...updated[tempId],
            actualFileId,
            status: 'processing',
            progress: 100
          };
          delete updated[tempId];
          return updated;
        });

        webSocket.joinFileRoom(actualFileId);
        toast.success(`File "${file.name}" uploaded successfully`);

      } catch (error) {
        console.error('Upload error:', error);
        setUploadingFiles(prev => ({
          ...prev,
          [tempId]: {
            ...prev[tempId],
            status: 'error',
            error: (error as Error).message
          }
        }));
        toast.error(`Failed to upload "${file.name}": ${(error as Error).message}`);
      }
    }
  }, [sessionId, webSocket]);

  // Monitor WebSocket updates
  useEffect(() => {
    const checkForUpdates = () => {
      setUploadingFiles(prev => {
        const updated = { ...prev };
        let hasChanges = false;

        Object.keys(updated).forEach(fileId => {
          const fileInfo = updated[fileId];
          if (!fileInfo.actualFileId) return;

          const fileStatus = webSocket.getFileStatus(fileInfo.actualFileId);
          const processingProgress = webSocket.getProcessingProgress(fileInfo.actualFileId);

          if (fileStatus) {
            updated[fileId] = {
              ...fileInfo,
              status: fileStatus.status,
              metadata: fileStatus.metadata,
              progress: fileStatus.progress || fileInfo.progress
            };
            hasChanges = true;

            if (fileStatus.status === 'processed') {
              toast.success(`File "${fileInfo.file.name}" processed successfully`);
              setTimeout(() => {
                setUploadingFiles(prev => {
                  const newState = { ...prev };
                  delete newState[fileId];
                  return newState;
                });
                if (onUploadComplete) onUploadComplete(fileInfo.actualFileId, fileStatus);
              }, 2000);
            } else if (fileStatus.status === 'error') {
              toast.error(`Failed to process "${fileInfo.file.name}"`);
            }
          }

          if (processingProgress) {
            updated[fileId] = {
              ...updated[fileId],
              processingStage: processingProgress.stage,
              processingProgress: processingProgress.progress,
              processingMessage: processingProgress.message
            };
            hasChanges = true;
          }
        });

        return hasChanges ? updated : prev;
      });
    };

    const interval = setInterval(checkForUpdates, 500);
    return () => clearInterval(interval);
  }, [webSocket, onUploadComplete]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploading':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'processed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <File className="h-4 w-4" />;
    }
  };

  const getStatusText = (file: UploadingFile) => {
    if (file.status === 'uploading') return 'Uploading...';
    if (file.status === 'processing') {
      if (file.processingStage && file.processingMessage) {
        return `${file.processingStage}: ${file.processingMessage}`;
      }
      return 'Processing...';
    }
    if (file.status === 'processed') return 'Completed';
    if (file.status === 'error') return `Error: ${file.error}`;
    return 'Unknown';
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <Card>
        <CardContent className="p-6">
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => document.getElementById('file-input')?.click()}
          >
            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              Drop files here or click to upload
            </p>
            <p className="text-sm text-gray-500">
              Supports PDF, Word, PowerPoint, Excel, and text files
            </p>
            <input
              id="file-input"
              type="file"
              accept={accept}
              multiple={multiple}
              className="hidden"
              onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Upload Progress */}
      {Object.keys(uploadingFiles).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Upload Progress</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(uploadingFiles).map(([fileId, fileInfo]) => (
              <div key={fileId} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(fileInfo.status)}
                    <span className="font-medium truncate max-w-xs">
                      {fileInfo.file.name}
                    </span>
                  </div>
                  <span className="text-sm text-gray-500">
                    {fileInfo.processingProgress || fileInfo.progress}%
                  </span>
                </div>
                <Progress
                  value={fileInfo.processingProgress || fileInfo.progress}
                  className="w-full"
                />
                <p className="text-xs text-gray-600">
                  {getStatusText(fileInfo)}
                </p>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* WebSocket Connection Status */}
      <div className="flex items-center space-x-2 text-sm">
        <div className={`w-2 h-2 rounded-full ${webSocket.isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
        <span className="text-gray-600">
          {webSocket.isConnected ? 'Real-time updates enabled' : 'Real-time updates disabled'}
        </span>
        {webSocket.connectionError && (
          <span className="text-red-500">({webSocket.connectionError})</span>
        )}
      </div>
    </div>
  );
};

export default FileUploadWithProgress;
