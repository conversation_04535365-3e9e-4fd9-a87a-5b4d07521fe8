
import { ChatSession, Document } from '@/types/chat';

import React, { useState, useEffect, useRef } from 'react';

import { ChevronDown, ChevronRight, MessageSquare, MoreVertical, Edit, Trash, FilePlus } from 'lucide-react';

import DocumentList from './DocumentList';

interface ChatSessionItemProps {
  session: ChatSession;
  isSelected: boolean;
  documents: Document[];
  selectedDocuments: string[];
  currentDocumentId?: string;
  onSessionSelect: (id: string) => void;
  onDocumentView: (doc: Document) => void;
  onEditSession?: (id: string) => void;
  onDeleteSession?: (id: string) => void;
  onAddDocumentToSession?: (id: string) => void;
  onDocumentDelete?: (id: string) => void;
  onFetchDocuments?: (id: string, forceRefresh?: boolean) => void;
  isLoadingDocuments?: boolean;
  isEditingSession?: boolean;
  isDeletingSession?: boolean;
  isUploadingDocument?: boolean;
}

const ChatSessionItem: React.FC<ChatSessionItemProps> = ({
  session,
  isSelected,
  documents,
  selectedDocuments,
  currentDocumentId,
  onSessionSelect,
  onDocumentView,
  onEditSession,
  onDeleteSession,
  onAddDocumentToSession,
  onDocumentDelete,
  onFetchDocuments,
  isLoadingDocuments = false,
  isEditingSession = false,
  isDeletingSession = false,
  isUploadingDocument = false
}) => {
  const [isExpanded, setIsExpanded] = useState(isSelected);
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const sessionDocuments = documents.filter(doc => session.documentIds.includes(doc.id));

  // Update expanded state when isSelected changes
  useEffect(() => {
    if (isSelected) {
      setIsExpanded(true);
    }
  }, [isSelected]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleToggleExpand = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);

    // If expanding and we have a fetch documents callback, call it
    if (newExpandedState && onFetchDocuments) {
      onFetchDocuments(session.id);
    }
  };

  const handleSessionClick = () => {
    onSessionSelect(session.id);
    setIsExpanded(true);

    // Fetch documents for this session
    if (onFetchDocuments) {
      onFetchDocuments(session.id);
    }
  };

  const handleToggleMenu = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setShowMenu(!showMenu);
  };

  const handleEditClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setShowMenu(false);
    if (onEditSession) {
      onEditSession(session.id);
    }
  };

  const handleDeleteClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setShowMenu(false);
    if (onDeleteSession) {
      onDeleteSession(session.id);
    }
  };

  const handleAddDocumentClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setShowMenu(false);
    if (onAddDocumentToSession) {
      onAddDocumentToSession(session.id);
    }
  };

  return (
    <div className="mb-1">
      <div
        className={`flex items-center gap-2 p-2 rounded-md cursor-pointer group ${
          isSelected ? 'bg-[#EDE9FE] text-[#8B5CF6]' : 'hover:bg-muted'
        }`}
        onClick={handleSessionClick}
      >
        <button
          className="p-1 rounded-sm hover:bg-gray-200"
          onClick={handleToggleExpand}
          aria-label={isExpanded ? "Collapse session" : "Expand session"}
          type="button"
        >
          {isExpanded ?
            <ChevronDown className="h-3 w-3 text-gray-500" /> :
            <ChevronRight className="h-3 w-3 text-gray-500" />
          }
        </button>
        <div className="w-5 h-5 flex items-center justify-center">
          <MessageSquare className="h-4 w-4" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium truncate">{session.name}</div>
        </div>

        {/* Three-dot menu button */}
        <div className="relative" ref={menuRef}>
          <button
            className="p-1 rounded-sm hover:bg-gray-200 opacity-0 group-hover:opacity-100 focus:opacity-100"
            onClick={handleToggleMenu}
            aria-label="More options"
            type="button"
          >
            <MoreVertical className="h-4 w-4 text-gray-500" />
          </button>

          {/* Dropdown menu */}
          {showMenu && (
            <div className="absolute right-0 mt-1 bg-white rounded-md shadow-lg z-10 py-1 border flex">
              <button
                className={`p-2 text-gray-700 hover:bg-gray-100 flex items-center justify-center ${
                  isEditingSession ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                onClick={handleEditClick}
                aria-label="Rename chat"
                disabled={isEditingSession || isDeletingSession || isUploadingDocument}
              >
                <Edit className="h-4 w-4" />
              </button>
              <button
                className={`p-2 text-gray-700 hover:bg-gray-100 flex items-center justify-center ${
                  isUploadingDocument ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                onClick={handleAddDocumentClick}
                aria-label="Add document"
                disabled={isEditingSession || isDeletingSession || isUploadingDocument}
              >
                <FilePlus className="h-4 w-4" />
              </button>
              <button
                className={`p-2 text-red-600 hover:bg-gray-100 flex items-center justify-center ${
                  isDeletingSession ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                onClick={handleDeleteClick}
                aria-label="Delete chat"
                disabled={isEditingSession || isDeletingSession || isUploadingDocument}
              >
                <Trash className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>
      </div>

      {isExpanded && (
        <div className="pl-8 pr-2 mt-1">
          <DocumentList
            documents={sessionDocuments}
            selectedDocuments={selectedDocuments}
            currentDocumentId={currentDocumentId}
            onDocumentView={onDocumentView}
            onDocumentDelete={onDocumentDelete}
            isViewMode={true}
            isLoading={isLoadingDocuments}
            showEmptyState={true}
          />
        </div>
      )}
    </div>
  );
};

export default ChatSessionItem;
