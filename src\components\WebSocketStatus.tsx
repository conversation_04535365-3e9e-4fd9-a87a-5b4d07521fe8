import React from 'react';
import { useWebSocketContext } from '../contexts/WebSocketContext';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Wifi, WifiOff, AlertCircle, RefreshCw, X } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';

interface WebSocketStatusProps {
  showText?: boolean;
  variant?: 'badge' | 'icon' | 'full' | 'detailed';
  className?: string;
  showControls?: boolean;
}

const WebSocketStatus: React.FC<WebSocketStatusProps> = ({
  showText = true,
  variant = 'full',
  className = '',
  showControls = false
}) => {
  const webSocket = useWebSocketContext();

  const getStatusColor = () => {
    if (webSocket.isConnected) return 'bg-green-500';
    if (webSocket.connectionError) return 'bg-red-500';
    return 'bg-yellow-500';
  };

  const getStatusText = () => {
    if (webSocket.isConnected) return 'Connected';
    if (webSocket.connectionError) return 'Error';
    return 'Connecting...';
  };

  const getStatusIcon = () => {
    if (webSocket.isConnected) return <Wifi className="h-4 w-4" />;
    if (webSocket.connectionError) return <AlertCircle className="h-4 w-4" />;
    return <WifiOff className="h-4 w-4" />;
  };

  const getTooltipContent = () => {
    if (webSocket.isConnected) {
      return 'WebSocket connected - Real-time updates enabled';
    }
    if (webSocket.connectionError) {
      return `WebSocket error: ${webSocket.connectionError}`;
    }
    return 'WebSocket connecting...';
  };

  if (variant === 'icon') {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`flex items-center ${className}`}>
            <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{getTooltipContent()}</p>
        </TooltipContent>
      </Tooltip>
    );
  }

  if (variant === 'badge') {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant={webSocket.isConnected ? 'default' : 'destructive'}
            className={className}
          >
            <div className={`w-2 h-2 rounded-full ${getStatusColor()} mr-2`} />
            {showText && getStatusText()}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{getTooltipContent()}</p>
        </TooltipContent>
      </Tooltip>
    );
  }

  // Detailed variant with controls
  if (variant === 'detailed') {
    return (
      <div className={`flex items-center justify-between p-3 bg-gray-50 rounded-lg border ${className}`}>
        <div className="flex items-center space-x-3">
          <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />
          <div>
            <div className="text-sm font-medium">
              {getStatusText()}
            </div>
            {webSocket.connectionError && (
              <div className="text-xs text-red-600">
                {webSocket.connectionError}
              </div>
            )}
          </div>
        </div>

        {showControls && (
          <div className="flex items-center space-x-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={webSocket.reconnect}
                  disabled={webSocket.isConnected}
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Reconnect WebSocket</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={webSocket.disconnect}
                  disabled={!webSocket.isConnected}
                >
                  <X className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Disconnect WebSocket</p>
              </TooltipContent>
            </Tooltip>
          </div>
        )}
      </div>
    );
  }

  // Full variant
  return (
    <div className={`flex items-center space-x-2 text-sm ${className}`}>
      <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
      {showText && (
        <span className="text-gray-600">
          {getStatusText()}
        </span>
      )}
      {webSocket.connectionError && (
        <Tooltip>
          <TooltipTrigger asChild>
            <AlertCircle className="h-4 w-4 text-red-500 cursor-help" />
          </TooltipTrigger>
          <TooltipContent>
            <p>Error: {webSocket.connectionError}</p>
          </TooltipContent>
        </Tooltip>
      )}
    </div>
  );
};

export default WebSocketStatus;
