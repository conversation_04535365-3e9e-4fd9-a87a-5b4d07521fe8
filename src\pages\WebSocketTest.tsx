import React, { useState } from 'react';
import { useWebSocketContext } from '../contexts/WebSocketContext';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import FileUploadWithProgress from '../components/FileUploadWithProgress';
import ChatWithStreaming from '../components/ChatWithStreaming';

const WebSocketTest: React.FC = () => {
  const webSocket = useWebSocketContext();
  const [testFileId, setTestFileId] = useState('test-file-123');
  const [testChatSessionId, setTestChatSessionId] = useState('test-chat-456');
  const [testMessage, setTestMessage] = useState('Hello, this is a test message!');

  const triggerFileUpdate = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL || process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/websocket/test/file-update`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          file_id: testFileId,
          status: 'processing',
          progress: Math.floor(Math.random() * 100),
          metadata: { stage: 'text_extraction', test: true }
        })
      });

      if (response.ok) {
        console.log('File update triggered successfully');
      }
    } catch (error) {
      console.error('Failed to trigger file update:', error);
    }
  };

  const triggerProcessingProgress = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL || process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/websocket/test/processing-progress`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          file_id: testFileId,
          stage: 'embedding',
          progress: Math.floor(Math.random() * 100),
          message: 'Generating embeddings...'
        })
      });

      if (response.ok) {
        console.log('Processing progress triggered successfully');
      }
    } catch (error) {
      console.error('Failed to trigger processing progress:', error);
    }
  };

  const triggerChatChunk = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL || process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/websocket/test/chat-chunk`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_session_id: testChatSessionId,
          chunk: testMessage,
          is_final: false,
          metadata: { test: true }
        })
      });

      if (response.ok) {
        console.log('Chat chunk triggered successfully');
      }
    } catch (error) {
      console.error('Failed to trigger chat chunk:', error);
    }
  };

  const joinFileRoom = () => {
    webSocket.joinFileRoom(testFileId);
  };

  const joinChatRoom = () => {
    webSocket.joinChatRoom(testChatSessionId);
  };

  const testChatHistoryAPI = async () => {
    try {
      console.log(`Testing chat history API for session: ${testChatSessionId}`);
      const response = await fetch(`${import.meta.env.VITE_API_URL || process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/chat/sessions/${testChatSessionId}/messages`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log('API Response Status:', response.status);
      console.log('API Response Headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API Error ${response.status}:`, errorText);
        return;
      }

      const data = await response.json();
      console.log('API Response Data:', data);

      if (data.messages && Array.isArray(data.messages)) {
        console.log(`Found ${data.messages.length} messages:`, data.messages);
      } else {
        console.log('No messages found or unexpected format');
      }
    } catch (error) {
      console.error('Failed to test chat history API:', error);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">WebSocket Test Page</h1>
        <p className="text-gray-600">Test real-time WebSocket functionality</p>
      </div>

      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            WebSocket Connection Status
            <Badge variant={webSocket.isConnected ? "default" : "destructive"}>
              {webSocket.isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {webSocket.connectionError && (
            <p className="text-red-500 text-sm">Error: {webSocket.connectionError}</p>
          )}
          <div className="grid grid-cols-2 gap-4 mt-4">
            <div>
              <p className="text-sm font-medium">File Updates:</p>
              <p className="text-xs text-gray-500">{Object.keys(webSocket.fileUpdates).length} active</p>
            </div>
            <div>
              <p className="text-sm font-medium">Chat Streams:</p>
              <p className="text-xs text-gray-500">{Object.keys(webSocket.chatStreams).length} active</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Room Management */}
      <Card>
        <CardHeader>
          <CardTitle>Room Management</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">File ID:</label>
              <Input
                value={testFileId}
                onChange={(e) => setTestFileId(e.target.value)}
                placeholder="Enter file ID"
              />
              <Button onClick={joinFileRoom} className="w-full">
                Join File Room
              </Button>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Chat Session ID:</label>
              <Input
                value={testChatSessionId}
                onChange={(e) => setTestChatSessionId(e.target.value)}
                placeholder="Enter chat session ID"
              />
              <Button onClick={joinChatRoom} className="w-full">
                Join Chat Room
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Triggers */}
      <Card>
        <CardHeader>
          <CardTitle>Test WebSocket Events</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button onClick={triggerFileUpdate} variant="outline">
              Trigger File Update
            </Button>
            <Button onClick={triggerProcessingProgress} variant="outline">
              Trigger Processing Progress
            </Button>
            <Button onClick={triggerChatChunk} variant="outline">
              Trigger Chat Chunk
            </Button>
            <Button onClick={testChatHistoryAPI} variant="outline" className="bg-blue-50 border-blue-200">
              Test Chat History API
            </Button>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Test Message:</label>
            <Input
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              placeholder="Enter test message"
            />
          </div>
        </CardContent>
      </Card>

      {/* File Upload Test */}
      <Card>
        <CardHeader>
          <CardTitle>File Upload with Real-time Progress</CardTitle>
        </CardHeader>
        <CardContent>
          <FileUploadWithProgress
            sessionId={testChatSessionId}
            onUploadComplete={(fileId, fileStatus) => {
              console.log('Upload completed:', fileId, fileStatus);
            }}
          />
        </CardContent>
      </Card>

      {/* Chat Streaming Test */}
      <Card>
        <CardHeader>
          <CardTitle>Chat with Streaming</CardTitle>
        </CardHeader>
        <CardContent>
          <ChatWithStreaming
            sessionId={testChatSessionId}
            documents={[
              { id: 'doc-1', filename: 'test-document.pdf', status: 'processed' },
              { id: 'doc-2', filename: 'sample-file.docx', status: 'processed' }
            ]}
            className="h-96"
          />
        </CardContent>
      </Card>

      {/* Debug Information */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">File Updates:</h4>
              <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                {JSON.stringify(webSocket.fileUpdates, null, 2)}
              </pre>
            </div>
            <div>
              <h4 className="font-medium mb-2">Processing Progress:</h4>
              <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                {JSON.stringify(webSocket.processingProgress, null, 2)}
              </pre>
            </div>
            <div>
              <h4 className="font-medium mb-2">Chat Streams:</h4>
              <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                {JSON.stringify(webSocket.chatStreams, null, 2)}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WebSocketTest;
