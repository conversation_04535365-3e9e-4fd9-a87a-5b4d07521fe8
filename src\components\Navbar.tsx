
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Link, useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '@/app/hooks';
import { selectUser, selectIsAuthenticated, logout } from '@/features/auth/authSlice';
import WebSocketStatus from './WebSocketStatus';

const Navbar: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const user = useAppSelector(selectUser);

  const handleLogout = async () => {
    await dispatch(logout());
    navigate('/');
  };

  return (
    <header className="fixed w-full z-50 bg-background/80 backdrop-blur-md border-b py-4">
      <div className="container flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Link to="/" className="flex items-center gap-2">
            <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-docai-purple to-docai-blue"></div>
            <span className="font-display text-xl font-semibold">AnyDocAI</span>
          </Link>
        </div>
        <nav className="hidden md:flex items-center gap-8">
          <Link to="/" className="text-sm font-medium hover:text-primary transition-colors">Home</Link>
          <a href="#features" className="text-sm font-medium hover:text-primary transition-colors">Features</a>
          <a href="#pricing" className="text-sm font-medium hover:text-primary transition-colors">Pricing</a>
          <a href="#faq" className="text-sm font-medium hover:text-primary transition-colors">FAQ</a>
          {isAuthenticated && (
            <>
              <Link to="/chat" className="text-sm font-medium hover:text-primary transition-colors">Chat</Link>
            </>
          )}
        </nav>
        <div className="flex items-center gap-4">
          {isAuthenticated ? (
            <>
              <WebSocketStatus variant="icon" className="hidden sm:flex" />
              <Link to="/dashboard" className="hidden sm:flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-slate-800 flex items-center justify-center text-white">
                  {user?.name?.charAt(0) || user?.full_name?.charAt(0) || 'U'}
                </div>
                <span className="text-sm font-medium">{user?.name || user?.full_name || 'User'}</span>
              </Link>
              <Button variant="outline" size="sm" onClick={handleLogout}>
                Log out
              </Button>
            </>
          ) : (
            <>
              <Link to="/auth">
                <Button variant="outline" size="sm" className="hidden sm:flex">
                  Log in
                </Button>
              </Link>
              <Link to="/auth">
                <Button size="sm">Try Now</Button>
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  );
};

export default Navbar;
