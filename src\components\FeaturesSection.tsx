
import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, Files, Search, MessageSquare, Users, Lock } from 'lucide-react';

const features = [
  {
    icon: <FileText className="h-6 w-6 text-docai-purple" />,
    title: "Chat with Any File",
    description: "Upload and chat with PDFs, Word docs, Excel sheets, PowerPoint presentations, and more."
  },
  {
    icon: <Files className="h-6 w-6 text-docai-blue" />,
    title: "Multi-File Intelligence",
    description: "Ask questions across multiple documents at once for deeper insights."
  },
  {
    icon: <Search className="h-6 w-6 text-docai-indigo" />,
    title: "Smart Search",
    description: "Find information inside hundreds of pages instantly across all your uploads."
  },
  {
    icon: <MessageSquare className="h-6 w-6 text-docai-pink" />,
    title: "Instant Summaries",
    description: "Condense long reports, papers, or slide decks into simple takeaways."
  },
  {
    icon: <Users className="h-6 w-6 text-docai-purple" />,
    title: "Team Collaboration",
    description: "Share chats and documents with teammates securely."
  },
  {
    icon: <Lock className="h-6 w-6 text-docai-blue" />,
    title: "Secure by Design",
    description: "Your files never leave your workspace and are stored securely on Wasabi S3."
  }
];

const FeaturesSection: React.FC = () => {
  return (
    <section id="features" className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="text-center space-y-4 max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl md:text-4xl font-bold">
            Everything you need for <span className="gradient-text">document intelligence</span>
          </h2>
          <p className="text-muted-foreground">
            Powered by GPT-4 Turbo and Claude 3, AnyDocAI understands your documents like a human expert — but responds in seconds.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="border bg-card h-full transition-all hover:shadow-md hover:-translate-y-1">
              <CardHeader>
                <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                  {feature.icon}
                </div>
                <CardTitle>{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
