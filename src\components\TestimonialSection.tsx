
import React from 'react';

const testimonials = [
  {
    quote: "AnyDocAI has transformed the way our legal team reviews documents. What used to take days now takes minutes.",
    name: "<PERSON><PERSON>",
    title: "Legal Director, Techstack Inc.",
  },
  {
    quote: "As a researcher, I can finally make sense of mountains of papers without spending weeks reading them all.",
    name: "Dr. <PERSON><PERSON>",
    title: "Research Scientist, Delhi University",
  },
  {
    quote: "We've reduced our document processing time by 78% since implementing AnyDocAI across our finance department.",
    name: "<PERSON><PERSON>",
    title: "CFO, GrowthWave Solutions",
  }
];

const TestimonialSection: React.FC = () => {
  return (
    <section className="py-16 bg-gradient-to-b from-background to-muted/30">
      <div className="container px-4 md:px-6">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">
          Trusted by businesses <span className="gradient-text">worldwide</span>
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-card border rounded-xl p-6 shadow-sm">
              <div className="mb-4 text-yellow-500">
                {"★".repeat(5)}
              </div>
              <blockquote className="text-lg mb-6">"{testimonial.quote}"</blockquote>
              <div>
                <p className="font-semibold">{testimonial.name}</p>
                <p className="text-sm text-muted-foreground">{testimonial.title}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialSection;
