
import React from 'react';
import { FileText } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

interface EmptyDocumentStateProps {
  onAddDocuments: () => void;
}

const EmptyDocumentState: React.FC<EmptyDocumentStateProps> = ({ onAddDocuments }) => {
  return (
    <div className="flex flex-col items-center justify-center h-full p-6 text-center">
      <FileText className="h-16 w-16 text-gray-300 mb-4" />
      <h3 className="text-lg font-medium mb-2">No documents attached</h3>
      <p className="text-gray-500 mb-6">
        This chat doesn't have any documents attached yet.
        Add a document to start analyzing it with AI.
      </p>
      <Button onClick={onAddDocuments} className="bg-[#8B5CF6] hover:bg-[#7C3AED]">
        Add documents
      </Button>
      
      <div className="grid grid-cols-2 gap-3 w-full max-w-sm mt-8">
        <Button variant="outline" className="text-sm justify-start px-3 h-auto py-2">
          What can you do?
        </Button>
        <Button variant="outline" className="text-sm justify-start px-3 h-auto py-2">
          Add documents
        </Button>
        <Button variant="outline" className="text-sm justify-start px-3 h-auto py-2">
          Supported formats
        </Button>
        <Button variant="outline" className="text-sm justify-start px-3 h-auto py-2">
          AI capabilities
        </Button>
      </div>
    </div>
  );
};

export default EmptyDocumentState;
