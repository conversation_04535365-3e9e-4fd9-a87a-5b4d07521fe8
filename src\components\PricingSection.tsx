
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Check } from 'lucide-react';

const plans = [
  {
    name: "Free",
    price: "₹0",
    description: "Try out the basics with limited features.",
    features: [
      "Upload up to 10 files/day",
      "Chat with single documents",
      "Basic summaries",
      "GPT-4 Turbo integration",
      "~2K tokens/day limit"
    ],
    highlighted: false,
    buttonText: "Sign up free"
  },
  {
    name: "Pro",
    price: "₹999",
    period: "/user/month",
    description: "Perfect for individuals and small teams.",
    features: [
      "Unlimited file uploads",
      "Multi-document chat",
      "Advanced summaries",
      "File organization & tags",
      "Team sharing (up to 3 users)",
      "~5K tokens/day"
    ],
    highlighted: true,
    buttonText: "Get started"
  },
  {
    name: "Ultimate",
    price: "₹1799",
    period: "/user/month",
    description: "For teams who need more power and flexibility.",
    features: [
      "Everything in Pro",
      "Claude 3 access",
      "API access",
      "Priority processing",
      "Enhanced team features",
      "Viewer roles",
      "~15K tokens/day"
    ],
    highlighted: false,
    buttonText: "Contact sales"
  }
];

const PricingSection: React.FC = () => {
  return (
    <section id="pricing" className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Simple, transparent pricing</h2>
          <p className="text-muted-foreground">
            Start for free, upgrade as your needs grow. All plans include core features.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {plans.map((plan, index) => (
            <Card key={index} className={`border ${plan.highlighted ? 'shadow-lg border-primary/50 shadow-primary/10' : ''}`}>
              <CardHeader>
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <div className="mt-2">
                  <span className="text-3xl font-bold">{plan.price}</span>
                  {plan.period && <span className="text-muted-foreground text-sm ml-1">{plan.period}</span>}
                </div>
                <CardDescription className="mt-2">{plan.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-start gap-2">
                      <Check className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button variant={plan.highlighted ? "default" : "outline"} className="w-full">
                  {plan.buttonText}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
        <div className="mt-12 text-center">
          <p className="text-muted-foreground text-sm">
            Need a custom enterprise plan? <a href="#" className="text-primary underline underline-offset-4">Contact our sales team</a>
          </p>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
