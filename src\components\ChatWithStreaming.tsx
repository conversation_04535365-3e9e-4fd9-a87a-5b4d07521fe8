import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useWebSocketContext } from '../contexts/WebSocketContext';
import { useAppSelector, useAppDispatch } from '../app/hooks';
import { fetchMessages, selectMessages, addMessage, sendMessage, selectIsLoadingMessages, selectIsSendingMessage } from '../features/chat/chatSlice';
import { ChatMessage } from '../types/chat';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { ScrollArea } from './ui/scroll-area';
import { Send, Bot, User, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

interface Document {
  id: string;
  filename: string;
  status: string;
}

interface ChatWithStreamingProps {
  sessionId: string;
  documents?: Document[];
  className?: string;
}

const ChatWithStreaming: React.FC<ChatWithStreamingProps> = ({
  sessionId,
  documents = [],
  className = ""
}) => {
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [hasJoinedRoom, setHasJoinedRoom] = useState(false);

  const webSocket = useWebSocketContext();
  const dispatch = useAppDispatch();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const streamingMessageRef = useRef<string>('');
  const prevSessionIdRef = useRef<string | null>(null);

  // Get messages from Redux store
  const reduxMessages = useAppSelector(selectMessages);
  const isLoadingHistory = useAppSelector(selectIsLoadingMessages);
  const isSendingMessage = useAppSelector(selectIsSendingMessage);

  // Convert Redux messages to component format
  const messages: Message[] = reduxMessages.map(msg => ({
    id: msg.id,
    type: msg.role === 'user' ? 'user' : 'assistant',
    content: msg.content,
    timestamp: msg.timestamp,
    metadata: msg.metadata || {}
  }));

  // Handle session changes and cleanup
  useEffect(() => {
    if (!sessionId) return;

    // Reset join state when sessionId changes
    if (prevSessionIdRef.current !== sessionId) {
      // Leave previous room if it exists
      if (prevSessionIdRef.current) {
        webSocket.leaveChatRoom(prevSessionIdRef.current);
      }
      setHasJoinedRoom(false);
      prevSessionIdRef.current = sessionId;
    }

    // Clear any existing streaming data for this session
    webSocket.clearChatStream(sessionId);
    setStreamingMessage('');
    setIsStreaming(false);
    setIsLoading(false);

    // Cleanup function to leave room when component unmounts or sessionId changes
    return () => {
      if (sessionId) {
        webSocket.leaveChatRoom(sessionId);
        setHasJoinedRoom(false);
      }
    };
  }, [sessionId, webSocket.clearChatStream, webSocket.leaveChatRoom]);

  // Join chat room after loading history (with duplicate prevention)
  useEffect(() => {
    if (sessionId && webSocket.isConnected && !isLoadingHistory && !hasJoinedRoom) {
      console.log(`Attempting to join chat room: ${sessionId}`);
      webSocket.joinChatRoom(sessionId);
      setHasJoinedRoom(true);
    }
  }, [sessionId, webSocket.isConnected, webSocket.joinChatRoom, isLoadingHistory, hasJoinedRoom]);

  // Monitor chat streams for new messages only
  useEffect(() => {
    const checkForStreams = () => {
      if (!sessionId || isLoadingHistory) return;

      const chatStream = webSocket.getChatStream(sessionId);
      if (chatStream) {
        setStreamingMessage(chatStream.text);
        setIsStreaming(!chatStream.isComplete);

        if (chatStream.isComplete && chatStream.text.trim()) {
          // Check if this message already exists to avoid duplicates
          const messageExists = messages.some(msg =>
            msg.content === chatStream.text &&
            msg.type === 'assistant'
          );

          if (!messageExists) {
            // Add completed streaming message to Redux
            const newMessage: ChatMessage = {
              id: Date.now().toString(),
              role: 'assistant',
              content: chatStream.text,
              timestamp: new Date().toISOString(),
              metadata: chatStream.metadata || {}
            };
            dispatch(addMessage(newMessage));
          }

          setStreamingMessage('');
          setIsStreaming(false);
          setIsLoading(false);
        }
      }
    };

    const interval = setInterval(checkForStreams, 100);
    return () => clearInterval(interval);
  }, [sessionId, webSocket.getChatStream, isLoadingHistory, messages, dispatch]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, streamingMessage]);

  const sendMessageHandler = useCallback(async () => {
    if (!inputMessage.trim() || isLoading || isSendingMessage || !sessionId) return;

    const userMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date().toISOString()
    };

    // Add user message to Redux immediately
    dispatch(addMessage(userMessage));
    setInputMessage('');
    setIsLoading(true);
    setIsStreaming(true);

    try {
      // Send message via Redux action with chat history
      const result = await dispatch(sendMessage({
        sessionId,
        message: userMessage.content,
        chatHistory: reduxMessages, // Pass original Redux messages with 'role' field
        useAgent: false
      })).unwrap();

      console.log('Message sent successfully:', result);

      // If WebSocket is connected, the response will come via WebSocket streaming
      if (webSocket.isConnected) {
        // Keep loading state for streaming, will be reset by stream monitor
        console.log('WebSocket connected, waiting for streaming response...');

        // Add a safety timeout to reset loading state if WebSocket doesn't respond
        setTimeout(() => {
          console.log('Checking if loading state needs to be reset...');
          setIsLoading(false);
          setIsStreaming(false);
        }, 10000); // 10 second timeout
      } else {
        // For non-WebSocket, reset loading state immediately since response is handled by Redux
        console.log('WebSocket not connected, using direct response');
        setIsLoading(false);
        setIsStreaming(false);
        toast.warning('Real-time streaming unavailable, using direct response');
      }

    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: ChatMessage = {
        id: `msg-${Date.now() + 1}`,
        role: 'assistant',
        content: `Error: ${(error as Error).message}`,
        timestamp: new Date().toISOString()
      };
      dispatch(addMessage(errorMessage));
      setIsLoading(false);
      setIsStreaming(false);
      toast.error('Failed to send message');
    }
  }, [inputMessage, isLoading, isSendingMessage, sessionId, messages, webSocket.isConnected, dispatch]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessageHandler();
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Render chat UI with streaming message display
  if (isLoadingHistory) {
    return (
      <Card className={`flex flex-col h-full ${className}`}>
        <CardHeader className="flex-shrink-0">
          <CardTitle>Chat Session</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center space-y-2">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <p className="text-sm text-gray-600">Loading chat history...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`flex flex-col h-full ${className}`}>
      <CardHeader className="flex-shrink-0">
        <CardTitle className="flex items-center justify-between">
          <span>Chat Session</span>
          <div className="flex items-center space-x-2 text-sm font-normal">
            <div className={`w-2 h-2 rounded-full ${webSocket.isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-gray-600">
              {webSocket.isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </CardTitle>
        {documents.length > 0 && (
          <p className="text-sm text-gray-600">
            Context: {documents.map(doc => doc.filename).join(', ')}
          </p>
        )}
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages Area */}
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex items-start space-x-3 ${
                  message.type === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.type === 'assistant' && (
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Bot className="h-4 w-4 text-blue-600" />
                  </div>
                )}

                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  <p className={`text-xs mt-1 ${
                    message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                  }`}>
                    {formatTimestamp(message.timestamp)}
                  </p>
                </div>

                {message.type === 'user' && (
                  <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-gray-600" />
                  </div>
                )}
              </div>
            ))}

            {/* Streaming Message */}
            {isStreaming && streamingMessage && (
              <div className="flex items-start space-x-3 justify-start">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Bot className="h-4 w-4 text-blue-600" />
                </div>
                <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-100 text-gray-900">
                  <p className="text-sm whitespace-pre-wrap">{streamingMessage}</p>
                  <div className="flex items-center space-x-1 mt-1">
                    <Loader2 className="h-3 w-3 animate-spin text-gray-500" />
                    <p className="text-xs text-gray-500">Typing...</p>
                  </div>
                </div>
              </div>
            )}

            {/* Loading indicator when no streaming */}
            {isLoading && !isStreaming && (
              <div className="flex items-start space-x-3 justify-start">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                </div>
                <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-100 text-gray-900">
                  <p className="text-sm text-gray-500">Thinking...</p>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="border-t p-4">
          <div className="flex space-x-2">
            <Input
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              disabled={isLoading || isSendingMessage}
              className="flex-1"
            />
            <Button
              onClick={sendMessageHandler}
              disabled={isLoading || isSendingMessage || !inputMessage.trim()}
              size="icon"
            >
              {(isLoading || isSendingMessage) ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ChatWithStreaming;
