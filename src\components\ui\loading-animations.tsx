import React from 'react';
import { cn } from '@/lib/utils';

// 1. Pulse Dots Animation (for general loading)
export const PulseDotsLoader: React.FC<{ className?: string; size?: 'sm' | 'md' | 'lg' }> = ({ 
  className, 
  size = 'md' 
}) => {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  return (
    <div className={cn("flex items-center justify-center space-x-1", className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            "bg-[#8B5CF6] rounded-full animate-pulse",
            sizeClasses[size]
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1s'
          }}
        />
      ))}
    </div>
  );
};

// 2. Spinning Gradient Ring (for API calls)
export const SpinningRingLoader: React.FC<{ className?: string; size?: number }> = ({ 
  className, 
  size = 40 
}) => (
  <div className={cn("flex items-center justify-center", className)}>
    <div
      className="rounded-full border-4 border-transparent bg-gradient-to-r from-[#8B5CF6] via-[#3B82F6] to-[#8B5CF6] animate-spin"
      style={{
        width: size,
        height: size,
        background: `conic-gradient(from 0deg, #8B5CF6, #3B82F6, #8B5CF6, transparent)`
      }}
    >
      <div 
        className="rounded-full bg-white"
        style={{
          width: size - 8,
          height: size - 8,
          margin: '2px'
        }}
      />
    </div>
  </div>
);

// 3. Bouncing Balls (for chat messages)
export const BouncingBallsLoader: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("flex items-center justify-center space-x-1", className)}>
    {[0, 1, 2].map((i) => (
      <div
        key={i}
        className="w-3 h-3 bg-gradient-to-r from-[#8B5CF6] to-[#3B82F6] rounded-full animate-bounce"
        style={{
          animationDelay: `${i * 0.1}s`,
          animationDuration: '0.6s'
        }}
      />
    ))}
  </div>
);

// 4. Morphing Squares (for document processing)
export const MorphingSquaresLoader: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("flex items-center justify-center", className)}>
    <div className="relative w-12 h-12">
      {[0, 1, 2, 3].map((i) => (
        <div
          key={i}
          className="absolute w-3 h-3 bg-[#8B5CF6] rounded-sm animate-pulse"
          style={{
            top: i < 2 ? '0' : '50%',
            left: i % 2 === 0 ? '0' : '50%',
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1.2s'
          }}
        />
      ))}
    </div>
  </div>
);

// 5. Wave Animation (for file uploads)
export const WaveLoader: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("flex items-center justify-center space-x-1", className)}>
    {[0, 1, 2, 3, 4].map((i) => (
      <div
        key={i}
        className="w-1 bg-gradient-to-t from-[#8B5CF6] to-[#3B82F6] rounded-full animate-pulse"
        style={{
          height: `${20 + Math.sin(i * 0.5) * 10}px`,
          animationDelay: `${i * 0.1}s`,
          animationDuration: '1s'
        }}
      />
    ))}
  </div>
);

// 6. Rotating Hexagon (for session creation)
export const RotatingHexagonLoader: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("flex items-center justify-center", className)}>
    <div className="relative w-8 h-8">
      <div className="absolute inset-0 border-2 border-[#8B5CF6] border-t-transparent rounded-full animate-spin" />
      <div className="absolute inset-1 border-2 border-[#3B82F6] border-b-transparent rounded-full animate-spin" 
           style={{ animationDirection: 'reverse', animationDuration: '0.8s' }} />
    </div>
  </div>
);

// 7. Typing Dots (for AI responses)
export const TypingDotsLoader: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("flex items-center space-x-1 p-3 bg-gray-100 rounded-lg", className)}>
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1.4s'
          }}
        />
      ))}
    </div>
  </div>
);

// 8. Progress Ring (for document analysis)
export const ProgressRingLoader: React.FC<{ 
  className?: string; 
  progress?: number;
  size?: number;
}> = ({ className, progress = 0, size = 60 }) => {
  const radius = (size - 8) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className={cn("flex items-center justify-center", className)}>
      <div className="relative" style={{ width: size, height: size }}>
        <svg
          className="transform -rotate-90"
          width={size}
          height={size}
        >
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#e5e7eb"
            strokeWidth="4"
            fill="transparent"
          />
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="url(#gradient)"
            strokeWidth="4"
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-300 ease-in-out"
          />
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#8B5CF6" />
              <stop offset="100%" stopColor="#3B82F6" />
            </linearGradient>
          </defs>
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-sm font-medium text-gray-700">{progress}%</span>
        </div>
      </div>
    </div>
  );
};

// 9. Floating Particles (for background loading)
export const FloatingParticlesLoader: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("relative w-16 h-16 flex items-center justify-center", className)}>
    {[0, 1, 2, 3, 4, 5].map((i) => (
      <div
        key={i}
        className="absolute w-2 h-2 bg-[#8B5CF6] rounded-full opacity-70"
        style={{
          animation: `float-${i} 2s infinite ease-in-out`,
          animationDelay: `${i * 0.2}s`,
          transform: `rotate(${i * 60}deg) translateX(20px)`
        }}
      />
    ))}
  </div>
);

// 10. DNA Helix (for complex processing)
export const DNAHelixLoader: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("flex items-center justify-center", className)}>
    <div className="relative w-12 h-12">
      {[0, 1].map((strand) => (
        <div key={strand} className="absolute inset-0">
          {[0, 1, 2, 3].map((i) => (
            <div
              key={i}
              className={`absolute w-2 h-2 rounded-full ${
                strand === 0 ? 'bg-[#8B5CF6]' : 'bg-[#3B82F6]'
              }`}
              style={{
                top: `${i * 25}%`,
                left: '50%',
                transform: `translateX(-50%) rotate(${i * 90 + strand * 45}deg) translateX(15px)`,
                animation: `helix-${strand} 2s infinite linear`,
                animationDelay: `${i * 0.1}s`
              }}
            />
          ))}
        </div>
      ))}
    </div>
  </div>
);
